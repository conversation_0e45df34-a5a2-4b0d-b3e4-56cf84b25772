import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal, message } from 'antd';
import './style.less';
import { FileOutlined, ExportOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@src/models/hooks';
import { selectInfo, selectPipelineId } from '@src/models/pipeline';
import { saveTaskList } from '@src/models/task';
import { savePipeline } from '@src/models/pipeline';
import api from '@src/api';
import image_1752559334301_g8mp37 from '../../public/assets/images/image_1752559334301_g8mp37.svg';
import image_1752559334865_a9ykfn from '../../public/assets/images/image_1752559334865_a9ykfn.svg';
import BlandAltmanPanel from './BlandAltmanPanel';
import { generateBlandAltmanFromModelResults } from './utils/blandAltmanUtils';
import LossCurvePanel from './LossCurvePanel';
import {
  generateValidationLossCurve,
  generateTrainLossCurve,
  generateTrainLossStepCurve,
  generateTestLossCurve,
} from './utils/lossCurveUtils';
interface TaskAnalysis {
  task_name: string;
  start_time: string;
  finish_time: string;
  duration_seconds: number;
  task_label: string;
  task_args: Record<string, any>;
}

interface ModelEvaluation {
  config: {
    model: {
      type: string;
      network: { name: string };
      in_channels: number;
      out_channels: number;
      features: number[];
      dropout: number;
      loss: { type: string; smooth: number };
      optimizer: { type: string; lr: number; weight_decay: number };
      scheduler?: { type: string; patience: number; factor: number; monitor: string };
      metrics: Record<string, any>;
    };
    data: {
      type: string;
      batch_size: number;
      data_dir: string;
      train_val_split: number[];
      train_transforms: Record<string, any>;
    };
    training: {
      max_epochs: number;
      devices: number;
      accelerator: string;
      precision: string;
      experiment_name: string;
      early_stopping: boolean;
      patience: number;
      monitor?: string;
      monitor_mode?: string;
    };
  };
  results: {
    train_val_metrics: Record<string, number>;
    test_metrics: Record<string, number>;
    best_model_path: string;
    best_model_score: number;
  };
}

interface ReportData {
  pipeline_id: string;
  generated_time: string;
  dataset_analysis: Record<string, any>;
  task_analysis: TaskAnalysis[];
  model_evaluation: ModelEvaluation;
}

interface ReportProps {
  reportUrl?: string;
  onTabChange?: (key: string) => void;
}

const Report: React.FC<ReportProps> = ({ reportUrl, onTabChange }) => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const dispatch = useAppDispatch();
  const pipelineInfo = useAppSelector(selectInfo);
  const pipelineId = useAppSelector(selectPipelineId);

  // 解构常用的数据路径
  const modelConfig = reportData?.model_evaluation.config;
  const modelResults = reportData?.model_evaluation.results;
  const trainValMetrics = modelResults?.train_val_metrics;
  const testMetrics = modelResults?.test_metrics;

  // 当有reportUrl时，自动获取报告数据
  useEffect(() => {
    if (reportUrl) {
      setLoading(true);
      setError(null);
      setReportData(null);

      fetch(reportUrl)
        .then(async (response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const jsonData = await response.json();
          console.log(JSON.stringify(jsonData));
          setReportData(jsonData);
        })
        .catch((error) => {
          console.error('获取报告数据失败:', error);
          setError('报告数据获取失败');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [reportUrl]);

  // 日志按钮点击事件 - 跳转到监控页面
  const handleLogClick = () => {
    if (onTabChange) {
      onTabChange('monitor');
    }
  };

  // 导出按钮点击事件 - 下载JSON文件
  const handleExport = () => {
    if (reportData) {
      const dataStr = JSON.stringify(reportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `report_${reportData.pipeline_id}_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('报告已导出');
    }
  };

  // 重新运行按钮点击事件
  const handleRerun = () => {
    if (!pipelineId) {
      message.error('未找到流水线ID');
      return;
    }

    Modal.confirm({
      title: '确定要重新运行吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(await saveTaskList());
          dispatch(savePipeline());
          const res = await api.pipeline_modelview_run(pipelineId);
          const { status, message: msg } = res;
          if (status === 0) {
            message.success(msg);
          } else {
            message.error(msg);
          }
        } catch (error) {
          message.error('重新运行失败');
          console.error('重新运行失败:', error);
        }
      },
    });
  };

  // 根据指标值生成动态条形图
  const generateMetricsBar = (value: number | undefined, metricType: 'dice' | 'iou' | 'loss') => {
    if (!value || value === 0) {
      // 如果没有值，显示5个灰色条
      return (
        <span className="metrics-bar">
          {Array.from({ length: 5 }, (_, index) => (
            <i key={index}></i>
          ))}
        </span>
      );
    }

    // 根据指标类型确定颜色和条数逻辑
    let color = '#eee'; // 默认灰色
    let barCount = 1;

    if (metricType === 'dice' || metricType === 'iou') {
      // DICE和IOU指标：值越高越好，范围通常0-1
      if (value >= 0.7) {
        color = '#56CA00'; // 绿色 - 优秀
        barCount = 5;
      } else if (value >= 0.5) {
        color = '#006BFF'; // 蓝色 - 良好
        barCount = 4;
      } else if (value >= 0.3) {
        color = '#006BFF'; // 蓝色 - 一般
        barCount = 3;
      } else if (value >= 0.1) {
        color = '#FF8D2F'; // 橙色 - 较差
        barCount = 2;
      } else {
        color = '#FF8D2F'; // 橙色 - 很差
        barCount = 1;
      }
    } else if (metricType === 'loss') {
      // LOSS指标：值越低越好
      if (value <= 0.2) {
        color = '#56CA00'; // 绿色 - 优秀
        barCount = 5;
      } else if (value <= 0.5) {
        color = '#006BFF'; // 蓝色 - 良好
        barCount = 4;
      } else if (value <= 0.8) {
        color = '#006BFF'; // 蓝色 - 一般
        barCount = 3;
      } else if (value <= 1.5) {
        color = '#FF8D2F'; // 橙色 - 较差
        barCount = 2;
      } else {
        color = '#FF8D2F'; // 橙色 - 很差
        barCount = 1;
      }
    }

    return (
      <span className="metrics-bar">
        {Array.from({ length: 5 }, (_, index) => (
          <i
            key={index}
            style={{
              background: index < barCount ? color : '#eee',
            }}
          ></i>
        ))}
      </span>
    );
  };

  return (
    <div className="report-page">
      <div className="report-container">
        <div className="report-header">
          <div className="header-left-line"></div>
          <div className="header-title">运行结果报告</div>
          <div className="header-right-line"></div>
        </div>
        <div className="report-main">
          <div className="report-row">
            {/* 模型结果 */}
            <div className="report-card model-result-card">
              <div className="card-title">
                <span className="dot-blue" />
                模型结果
              </div>
              <div className="metrics-table-v2">
                <div className="metrics-header">
                  <div className="metrics-cell header">数据集</div>
                  <div className="metrics-cell">
                    DICE
                    <span className="metrics-sub">指标1</span>
                  </div>
                  <div className="metrics-cell">
                    IOU
                    <span className="metrics-sub">指标2</span>
                  </div>
                  <div className="metrics-cell">
                    LOSS
                    <span className="metrics-sub">指标3</span>
                  </div>
                </div>
                {/* 训练集 */}
                <div className="metrics-row">
                  <div className="metrics-cell label">训练集</div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['train/train_dice']?.toString() || '-'}>
                      {trainValMetrics?.['train/train_dice']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['train/train_dice'], 'dice')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['train/train_iou']?.toString() || '-'}>
                      {trainValMetrics?.['train/train_iou']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['train/train_iou'], 'iou')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['train/train_loss_epoch']?.toString() || '-'}>
                      {trainValMetrics?.['train/train_loss_epoch']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['train/train_loss_epoch'], 'loss')}
                  </div>
                </div>
                {/* 验证集 */}
                <div className="metrics-row">
                  <div className="metrics-cell label">验证集</div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['val/val_dice']?.toString() || '-'}>
                      {trainValMetrics?.['val/val_dice']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['val/val_dice'], 'dice')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['val/val_iou']?.toString() || '-'}>
                      {trainValMetrics?.['val/val_iou']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['val/val_iou'], 'iou')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={trainValMetrics?.['val/val_loss']?.toString() || '-'}>
                      {trainValMetrics?.['val/val_loss']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(trainValMetrics?.['val/val_loss'], 'loss')}
                  </div>
                </div>
                {/* 测试集 */}
                <div className="metrics-row">
                  <div className="metrics-cell label">测试集</div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={testMetrics?.['test/test_dice']?.toString() || '-'}>
                      {testMetrics?.['test/test_dice']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(testMetrics?.['test/test_dice'], 'dice')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={testMetrics?.['test/test_iou']?.toString() || '-'}>
                      {testMetrics?.['test/test_iou']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(testMetrics?.['test/test_iou'], 'iou')}
                  </div>
                  <div className="metrics-cell">
                    <span className="metrics-value" title={testMetrics?.['test/test_loss']?.toString() || '-'}>
                      {testMetrics?.['test/test_loss']?.toFixed(3) || '-'}
                    </span>
                    {generateMetricsBar(testMetrics?.['test/test_loss'], 'loss')}
                  </div>
                </div>
              </div>
            </div>
            {/* 数据信息 */}
            <div className="report-card data-info-card">
              <div className="card-title">
                <span className="dot-blue" />
                数据情况
              </div>
              <div className="data-info-cards">
                <div className="data-info-item">
                  <div className="data-info-main">
                    <span className="data-info-value">
                      {modelConfig
                        ? Math.round((modelConfig.data.train_val_split[0] + modelConfig.data.train_val_split[1]) * 5000).toLocaleString()
                        : '-'}
                    </span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">总数据量</div>
                  <div className="data-info-divider"></div>
                  <div className="data-info-main">
                    <span className="data-info-value">{modelConfig?.model.out_channels || '-'}</span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">标签总类</div>
                </div>
                <div className="data-info-item">
                  <div className="data-info-main">
                    <span className="data-info-value">
                      {modelConfig ? Math.round(modelConfig.data.train_val_split[0] * 5000).toLocaleString() : '-'}
                    </span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">训练集</div>
                  <div className="data-info-divider"></div>
                  <div className="data-info-main">
                    <span className="data-info-value">{modelConfig?.data.batch_size || '-'}</span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">批次大小</div>
                </div>
                <div className="data-info-item">
                  <div className="data-info-main">
                    <span className="data-info-value">
                      {modelConfig ? Math.round(modelConfig.data.train_val_split[1] * 5000).toLocaleString() : '-'}
                    </span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">验证集</div>
                  <div className="data-info-divider"></div>
                  <div className="data-info-main">
                    <span className="data-info-value">{modelConfig?.model.in_channels || '-'}</span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">输入通道</div>
                </div>
                <div className="data-info-item">
                  <div className="data-info-main">
                    <span className="data-info-value">
                      {modelConfig
                        ? Math.round(
                            (1 - modelConfig.data.train_val_split[0] - modelConfig.data.train_val_split[1]) * 5000,
                          ).toLocaleString()
                        : '-'}
                    </span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">测试集</div>
                  <div className="data-info-divider"></div>
                  <div className="data-info-main">
                    <span className="data-info-value">{modelConfig?.training.devices || '-'}</span>
                    <span className="data-info-unit">个</span>
                  </div>
                  <div className="data-info-desc">GPU设备</div>
                </div>
              </div>
            </div>
          </div>
          <div className="report-row bottom-row">
            {/* 数据配置 */}
            <div className="report-card data-config-card">
              <div className="card-title">
                <span className="dot-blue" />
                数据配置
              </div>
              <div className="data-config-content">
                {/* 数据预处理 */}
                <div className="data-config-section">
                  <div className="config-section-title">数据预处理</div>
                  <div className="data-preprocess-list">
                    <div className="preprocess-item">
                      <div className="preprocess-value">{modelConfig?.data.train_transforms?.Resized?.spatial_size?.join(',') || '-'}</div>
                      <div className="preprocess-label">维度尺寸</div>
                    </div>
                    <div className="preprocess-item">
                      <div className="preprocess-value">{modelConfig?.data.train_transforms?.Resized?.mode?.[0] || '-'}</div>
                      <div className="preprocess-label">Resize方式</div>
                    </div>
                    <div className="preprocess-item">
                      <div className="preprocess-value">{modelConfig?.data.train_transforms?.NormalizeIntensityd ? '是' : '-'}</div>
                      <div className="preprocess-label">数据归一化</div>
                    </div>
                  </div>
                </div>
                {/* 数据增强方式 */}
                <div className="data-config-section">
                  <div className="config-section-title">数据增强方式</div>
                  <div className="augment-list">
                    {modelConfig?.data.train_transforms ? (
                      Object.keys(modelConfig.data.train_transforms)
                        .filter((key) => key.startsWith('Rand'))
                        .map((key, index) => (
                          <div key={index} className="augment-item">
                            {key}
                          </div>
                        ))
                    ) : (
                      <div className="augment-item">-</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* 模型训练参数 */}
            <div className="report-card model-param-card-v3">
              <div className="card-title">
                <span className="dot-blue" />
                模型训练参数
              </div>
              <div className="model-param-table">
                {/* 模型信息和正则化在一行显示 */}
                <div className="param-sections-row">
                  <div className="param-section">
                    <div className="section-title">模型信息</div>
                    <div className="param-row">
                      <div className="param-item">
                        <div className="param-value">{modelConfig?.model.network.name || '-'}</div>
                        <div className="param-label">模型名称</div>
                      </div>
                      <div className="param-item">
                        <div className="param-value">{modelConfig?.model.type || '-'}</div>
                        <div className="param-label">任务类别</div>
                      </div>
                    </div>
                  </div>

                  <div className="param-section">
                    <div className="section-title">正则化</div>
                    <div className="param-row">
                      <div className="param-item">
                        <div className="param-value">
                          {(() => {
                            const hasDropout = modelConfig?.model.dropout && modelConfig.model.dropout > 0;
                            const hasWeightDecay =
                              modelConfig?.model.optimizer.weight_decay && modelConfig.model.optimizer.weight_decay > 0;

                            if (hasDropout && hasWeightDecay) {
                              return 'Dropout + L2';
                            } else if (hasDropout) {
                              return 'Dropout';
                            } else if (hasWeightDecay) {
                              return 'L2正则化';
                            } else {
                              return '-';
                            }
                          })()}
                        </div>
                        <div className="param-label">类别</div>
                      </div>
                      <div className="param-item">
                        <div className="param-value">{modelConfig?.model.dropout || '-'}</div>
                        <div className="param-label">参数</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 损失函数设置行 */}
                <div className="param-section">
                  <div className="section-title">损失函数设置</div>
                  <div className="param-row">
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.model.loss.type || '-'}</div>
                      <div className="param-label">类别</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.model.optimizer.type || '-'}</div>
                      <div className="param-label">优化器</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.model.optimizer.lr || '-'}</div>
                      <div className="param-label">学习率</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.model.scheduler ? '是' : '-'}</div>
                      <div className="param-label">学习率调度器</div>
                    </div>
                  </div>
                </div>

                {/* 训练设置行 */}
                <div className="param-section">
                  <div className="section-title">训练设置</div>
                  <div className="param-row">
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.training.max_epochs || '-'}</div>
                      <div className="param-label">迭代次数</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.data.batch_size || '-'}</div>
                      <div className="param-label">读取批次</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">{modelConfig?.training.monitor || '-'}</div>
                      <div className="param-label">训练监控参数</div>
                    </div>
                    <div className="param-item">
                      <div className="param-value">
                        {modelConfig?.training.early_stopping ? `是（${modelConfig.training.patience || '-'}）` : '-'}
                      </div>
                      <div className="param-label">早停设置</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* LOSS曲线 */}
          {modelResults &&
            reportData &&
            (() => {
              // loss曲线数据在 model_evaluation 对象内部
              const fullData = reportData.model_evaluation as any;
              const lossCurves = [];

              // 检查验证损失曲线
              if (fullData['val/val_loss'] && fullData['val/val_loss'].steps && fullData['val/val_loss'].values) {
                lossCurves.push({
                  key: 'validation',
                  data: generateValidationLossCurve(fullData),
                  title: 'val_loss',
                });
              }

              // 检查训练损失曲线（按epoch）
              if (
                fullData['train/train_loss_epoch'] &&
                fullData['train/train_loss_epoch'].steps &&
                fullData['train/train_loss_epoch'].values
              ) {
                lossCurves.push({
                  key: 'train-epoch',
                  data: generateTrainLossCurve(fullData),
                  title: 'train_loss_epoch',
                });
              }

              // // 检查训练损失曲线（按step）
              // if (
              //   fullData['train/train_loss_step'] &&
              //   fullData['train/train_loss_step'].steps &&
              //   fullData['train/train_loss_step'].values
              // ) {
              //   lossCurves.push({
              //     key: 'train-step',
              //     data: generateTrainLossStepCurve(fullData),
              //     title: 'train_loss_step',
              //   });
              // }

              // // 检查测试损失曲线
              // if (fullData['test/test_loss'] && fullData['test/test_loss'].steps && fullData['test/test_loss'].values) {
              //   lossCurves.push({
              //     key: 'test',
              //     data: generateTestLossCurve(fullData),
              //     title: 'test_loss',
              //   });
              // }

              return (
                <div className="report-row">
                  <div className="report-card loss-curves-container">
                    <div className="card-title">
                      <span className="dot-blue" />
                      LOSS曲线
                    </div>
                    <div className="loss-curves-grid">
                      {lossCurves.map((curve) => (
                        <div key={curve.key} className="loss-curve-item">
                          <LossCurvePanel data={curve.data} title={curve.title} />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })()}

          {/* Bland-Altman图表 */}
          {/* {reportData &&
            (() => {
              // 从 model_evaluation 对象中获取时间序列数据
              const fullData = reportData.model_evaluation as any;
              const blandAltmanCharts: Array<{
                key: string;
                data: any;
                title: string;
                dataKey: string;
                description: string;
              }> = [];

              // 一次性生成所有 Bland-Altman 数据，避免重复计算
              const blandAltmanData = generateBlandAltmanFromModelResults(fullData);

              // 定义可用的图表配置
              const chartConfigs = [
                {
                  key: 'train-val-loss',
                  title: '训练损失 vs 验证损失',
                  dataKey: 'train' as const,
                  description: '比较训练损失和验证损失在相同epoch的差异分布',
                  requiredData: ['train/train_loss_epoch', 'val/val_loss'],
                },
                {
                  key: 'val-dice-iou',
                  title: 'DICE vs IOU 指标',
                  dataKey: 'validation' as const,
                  description: '比较验证集上DICE和IOU指标的一致性',
                  requiredData: ['val/val_dice', 'val/val_iou'],
                },
                {
                  key: 'test-val-loss',
                  title: '测试损失 vs 验证损失',
                  dataKey: 'test' as const,
                  description: '比较测试损失和验证损失的差异（基于最终值）',
                  requiredData: ['test/test_loss', 'val/val_loss'],
                },
              ];

              // 根据数据可用性和生成结果添加图表
              chartConfigs.forEach((config) => {
                // 检查必需的数据是否存在
                const hasRequiredData = config.requiredData.every((dataKey) => fullData[dataKey]?.steps && fullData[dataKey]?.values);

                // 检查是否成功生成了对应的数据
                const generatedData = blandAltmanData[config.dataKey];
                const hasGeneratedData = generatedData && generatedData.length > 0;

                if (hasRequiredData && hasGeneratedData) {
                  blandAltmanCharts.push({
                    key: config.key,
                    data: blandAltmanData,
                    title: config.title,
                    dataKey: config.dataKey,
                    description: config.description,
                  });
                }
              });

              return blandAltmanCharts.length > 0 ? (
                <div className="report-row">
                  <div className="report-card bland-altman-card">
                    <div className="card-title">
                      <span className="dot-blue" />
                      Bland-Altman 图
                    </div>
                    <div className="bland-altman-grid">
                      {blandAltmanCharts.map((chart) => (
                        <div key={chart.key} className="bland-altman-item">
                          <BlandAltmanPanel
                            data={{
                              [chart.dataKey]: (chart.data as any)[chart.dataKey],
                            }}
                            title={chart.title}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : null;
            })()} */}
        </div>

        {/* 底部操作栏 */}
        <div className="report-footer">
          <div className="footer-btn-container">
            <Button
              icon={<img src={image_1752559334865_a9ykfn} alt="日志" />}
              className="footer-btn-file"
              title="日志"
              onClick={handleLogClick}
            />
            <div className="footer-btn-group">
              <Button
                className="footer-btn export-btn"
                icon={<img src={image_1752559334301_g8mp37} alt="导出" />}
                disabled={!reportData}
                onClick={handleExport}
              >
                导出
              </Button>
              <Button type="primary" className="footer-btn rerun-btn" onClick={handleRerun}>
                重新运行
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Report;
