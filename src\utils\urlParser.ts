/**
 * URL参数解析工具
 * 支持解析复杂URL结构，包括hash中的参数
 */

export interface UrlParams {
    [key: string]: string | string[];
  }
  
  /**
   * 解析URL中的所有参数
   * @param url 要解析的URL，如果不提供则使用当前页面URL
   * @returns 解析后的参数对象
   */
  export function parseUrlParams(url?: string): UrlParams {
    const targetUrl = url || window.location.href;
    const params: UrlParams = {};
    
    try {
      const urlObj = new URL(targetUrl);
      
      // 解析search参数
      urlObj.searchParams.forEach((value, key) => {
        if (params[key]) {
          // 如果参数已存在，转换为数组
          if (Array.isArray(params[key])) {
            (params[key] as string[]).push(value);
          } else {
            params[key] = [params[key] as string, value];
          }
        } else {
          params[key] = value;
        }
      });
      
      // 解析hash中的参数
      if (urlObj.hash) {
        const hashParams = parseHashParams(urlObj.hash);
        Object.assign(params, hashParams);
      }
      
    } catch (error) {
      console.warn('URL解析失败:', error);
    }
    
    return params;
  }
  
  /**
   * 解析hash中的参数
   * @param hash hash字符串
   * @returns 解析后的参数对象
   */
  export function parseHashParams(hash: string): UrlParams {
    const params: UrlParams = {};
    
    // 移除开头的#
    const hashContent = hash.startsWith('#') ? hash.substring(1) : hash;
    
    // 查找hash中的查询参数部分
    const queryIndex = hashContent.indexOf('?');
    if (queryIndex !== -1) {
      const queryString = hashContent.substring(queryIndex + 1);
      const searchParams = new URLSearchParams(queryString);
      
      searchParams.forEach((value, key) => {
        if (params[key]) {
          if (Array.isArray(params[key])) {
            (params[key] as string[]).push(value);
          } else {
            params[key] = [params[key] as string, value];
          }
        } else {
          params[key] = value;
        }
      });
    }
    
    return params;
  }
  
  /**
   * 获取指定参数的值
   * @param paramName 参数名
   * @param url 要解析的URL，如果不提供则使用当前页面URL
   * @returns 参数值，如果不存在返回undefined
   */
  export function getUrlParam(paramName: string, url?: string): string | string[] | undefined {
    const params = parseUrlParams(url);
    return params[paramName];
  }
  
  
  /**
   * 检查URL是否包含指定参数
   * @param paramName 参数名
   * @param url 要检查的URL，如果不提供则使用当前页面URL
   * @returns 是否包含该参数
   */
  export function hasUrlParam(paramName: string, url?: string): boolean {
    const params = parseUrlParams(url);
    return paramName in params;
  }