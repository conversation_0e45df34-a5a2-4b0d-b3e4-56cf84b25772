import React, { useState, useEffect } from 'react';
import { Button, Tabs, Image, Spin } from 'antd';
import './style.less';
import image_1750929877875_ut69wg from '../../public/assets/images/image_1750929877875_ut69wg.png';
import { useAppSelector } from '@src/models/hooks';
import { selectPipelineId } from '@src/models/pipeline';
import { selectElements } from '@src/models/element';
import axios from 'axios';
import api from '../../api';

const { TabPane } = Tabs;

const tabItems = [
  { key: 'metrics', label: '训练指标' },
  { key: 'monitor', label: '资源监控' },
];

const Monitor: React.FC = () => {
  const [activeTab, setActiveTab] = useState('metrics');
  const [tensorboardUrl, setTensorboardUrl] = useState<string>('');
  const pipelineId = useAppSelector(selectPipelineId);
  const elements = useAppSelector(selectElements);
  const [loading, setLoading] = useState(false); // 新增loading状态

  // 切换到 metrics 时获取 tensorboard url 和 task args
  useEffect(() => {
    if (activeTab === 'metrics' && pipelineId && elements.length > 0) {
      const dataSetNodes = elements.filter((el: any) => el.type === 'dataSet');
      if (dataSetNodes.length > 0) {
        const lastNode = dataSetNodes
          .slice()
          .sort((a: any, b: any) => Number(a.id) - Number(b.id))
          .pop();
        const lastTaskId = lastNode?.id;
        if (lastTaskId) {
          // 获取args
          api.task_modelview_get(lastTaskId).then((res: any) => {
            if (res.status === 0) {
              let logdir = '/data/k8s/kubeflow/dataset/output'; // 默认值
              try {
                const argsObj = JSON.parse(res.result?.args || '{}');
                if (argsObj['--output-dir']) {
                  logdir = argsObj['--output-dir'];
                }
              } catch (e) {
                // 解析失败用默认值
              }
              // tensorboard逻辑
              const data = {
                logdir,
                name: 'default',
                namespace: 'default',
                task_id: lastTaskId,
              };
              api
                .tensorboard_smart_start(data)
                .then((tbRes: any) => {
                  // 根据返回内容判断是否loading
                  if (tbRes?.status !== 'exists') {
                    setLoading(true);
                  } else {
                    setLoading(false);
                    setTensorboardUrl(process.env.REACT_APP_TENSORBOARD_URL || '');
                  }
                })
                .catch(() => {
                  setLoading(false);
                });
            }
          });
        }
      }
    }
  }, [activeTab, pipelineId, elements]);

  return (
    <div className="monitor-page">
      {/* 顶部Ant Tabs栏 */}
      <Tabs defaultActiveKey="metrics" className="monitor-ant-tabs" onChange={setActiveTab}>
        {tabItems.map((tab) => (
          <TabPane tab={tab.label} key={tab.key} />
        ))}
      </Tabs>
      {/* 中间大图标和卡片 */}
      <div className={`monitor-center-content ${activeTab === 'metrics' ? 'tensorboard-active' : ''}`}>
        {activeTab === 'metrics' && (
          <div className="monitor-icon-wrapper tensorboard-wrapper">
            {/* Loading 状态 */}
            {loading ? (
              <div style={{ textAlign: 'center', padding: '40px 0', fontSize: 18 }}>
                <Spin tip="请稍等，正在加载..." size="large" style={{ margin: '0 auto' }} />
              </div>
            ) : (
              <div className="monitor-tensorboard-container">
                <iframe
                  src={tensorboardUrl}
                  width="100%"
                  height="100%"
                  style={{ border: 'none' }}
                  title="TensorBoard"
                  className="monitor-tensorboard-iframe"
                />
              </div>
            )}
          </div>
        )}
        {activeTab === 'monitor' && (
          <div className="monitor-icon-wrapper">
            {/* 占位大图标*/}
            <div className="monitor-floating-card">
              <Image src={image_1750929877875_ut69wg} preview={false}></Image>
            </div>
          </div>
        )}
        {/* 前往查看按钮 - 只在资源监控时显示 */}
        {activeTab === 'monitor' && (
          <Button
            type="primary"
            size="large"
            className="monitor-center-btn"
            onClick={() => {
              window.open(`${process.env.REACT_APP_BASE_URL}/grafana/d/pod-info/pod-info?orgId=1&refresh=5s&from=now-15m&to=now`, '_blank');
            }}
          >
            前往查看
          </Button>
        )}
      </div>
    </div>
  );
};

export default Monitor;
