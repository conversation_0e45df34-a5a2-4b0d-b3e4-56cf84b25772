import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, SearchBox, Text, FocusZone, IconButton, Icon, Spinner, SpinnerSize, Callout, Image } from '@fluentui/react';
import { useAppDispatch, useAppSelector } from '@src/models/hooks';
import { updateErrMsg } from '@src/models/app';
import { selectShow } from '@src/models/template';
import api from '@src/api';
import storage from '@src/utils/storage';
import { debounce } from '@src/utils';
import ModuleItem from './components/ModuleItem';
import SearchItem from './components/SearchItem';
import { useTranslation } from 'react-i18next';
import image_1750237685965_xs2mln from '../../public/assets/images/image_1750237685965_xs2mln.svg';
import image_1750237692610_nidqd3 from '../../public/assets/images/image_1750237692610_nidqd3.svg';
import image_1718702934218_jqz6lu from '../../public/assets/images/image_1718702934218_jqz6lu.svg';
import image_1718702934218_fayvt7 from '../../public/assets/images/image_1718702934218_fayvt7.svg';
import image_1718702934217_i5x3bh from '../../public/assets/images/image_1718702934217_i5x3bh.svg';
import image_1718702934217_kqu0mg from '../../public/assets/images/image_1718702934217_kqu0mg.svg';
import image_1718702934216_1kw9je from '../../public/assets/images/image_1718702934216_1kw9je.svg';
import image_1718702934216_tysn1v from '../../public/assets/images/image_1718702934216_tysn1v.svg';
import image_1718702934215_g4nuwp from '../../public/assets/images/image_1718702934215_g4nuwp.svg';
import image_1718702934215_a93jyd from '../../public/assets/images/image_1718702934215_a93jyd.svg';
import image_1718702934214_u6b34x from '../../public/assets/images/image_1718702934214_u6b34x.svg';
import image_1718702934214_hz0nqf from '../../public/assets/images/image_1718702934214_hz0nqf.svg';
import image_1718702934213_8cq7dn from '../../public/assets/images/image_1718702934213_8cq7dn.svg';
import image_1718702934213_ozr5lv from '../../public/assets/images/image_1718702934213_ozr5lv.svg';
import image_1718702934212_d9tqbp from '../../public/assets/images/image_1718702934212_d9tqbp.svg';
import image_1718702934212_m0a72s from '../../public/assets/images/image_1718702934212_m0a72s.svg';
import image_1718702934211_3xqz9u from '../../public/assets/images/image_1718702934211_3xqz9u.svg';
import image_1750645936565_tnjpyi from '../../public/assets/images/image_1750645936565_tnjpyi.svg';
import image_1750645938716_yy0iw7 from '../../public/assets/images/image_1750645938716_yy0iw7.svg';
import image_1750657959907_ie5fqt from '../../public/assets/images/image_1750657959907_ie5fqt.svg';
import './style.less';

const { Item } = Stack;

// 图标
const imageIcons = [
  {
    name: '基础命令',
    image: image_1718702934218_jqz6lu,
  },
  {
    name: '数据导入导出',
    image: image_1718702934218_fayvt7,
  },
  {
    name: '数据处理工具',
    image: image_1718702934217_i5x3bh,
  },
  {
    name: '机器学习框架',
    image: image_1718702934215_g4nuwp,
  },
  {
    name: '机器学习算法',
    image: image_1718702934215_a93jyd,
  },
  {
    name: '深度学习',
    image: image_1718702934214_u6b34x,
  },
  {
    name: '模型服务化',
    image: image_1718702934213_ozr5lv,
  },
  {
    name: '多媒体类模板',
    image: image_1718702934212_d9tqbp,
  },
  {
    name: '机器视觉',
    image: image_1718702934212_m0a72s,
  },
  {
    name: '大模型',
    image: image_1718702934211_3xqz9u,
  },
];

const ColorList: Array<{
  color: string;
  bg: string;
}> = [
  {
    color: 'rgba(0,120,212,1)',
    bg: 'rgba(0,120,212,0.02)',
  },
  {
    color: 'rgba(0,170,200,1)',
    bg: 'rgba(0,170,200,0.02)',
  },
  {
    color: 'rgba(0,200,153,1)',
    bg: 'rgba(0,200,153,0.02)',
  },
  {
    color: 'rgba(0,6,200,1)',
    bg: 'rgba(0,6,200,0.02)',
  },
  {
    color: 'rgba(212,65,0,1)',
    bg: 'rgba(212,65,0,0.02)',
  },
  {
    color: 'rgba(212,176,0,1)',
    bg: 'rgba(212,176,0,0.02)',
  },
];

const ModuleTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const show = useAppSelector(selectShow);
  const [expandNodes, setExpandNodes] = useState(new Set()); // 记录展开的模板节点
  const [nodeMap, setNodeMap] = useState(new Map()); // 模板节点分类
  const [nodeCount, setNodeCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchResult, setSearchResult] = useState(new Map());

  const { t, i18n } = useTranslation();

  const handleTemplateData = (res: any) => {
    console.log('handleTemplateData 开始处理数据:', res);

    if (!res || !Array.isArray(res)) {
      console.error('handleTemplateData: 数据格式错误，期望数组，实际:', typeof res, res);
      return;
    }

    const dataSet = new Map();
    let count = 0;

    res.forEach((ele: any, index: number) => {
      try {
        if (!ele || typeof ele !== 'object') {
          console.warn(`handleTemplateData: 跳过无效数据项 ${index}:`, ele);
          return;
        }

        if (ele.version !== 'Release') return;

        count += 1;

        // 查找对应的图标
        const iconObj = imageIcons.find((icon) => icon.name === ele.project?.name);

        const baseInfo = {
          id: ele.id,
          args: ele.args ? JSON.parse(ele.args) : {},
          name: ele.name,
          version: ele.version,
          describe: ele.describe,
          imagesName: ele.images?.name || '',
          createdBy: ele.created_by?.username || '',
          lastChanged: ele.changed_on,
          expand: ele.expand ? JSON.parse(ele.expand) : [],
          color: ColorList[(ele.project?.id || 0) % ColorList.length],
          icon: iconObj ? iconObj.image : imageIcons[Math.floor(Math.random() * imageIcons.length)].image, // 没有匹配时随机一个
        };

        const projectId = ele.project?.id;
        if (projectId === undefined || projectId === null) {
          console.warn(`handleTemplateData: 跳过没有项目ID的数据项 ${index}:`, ele);
          return;
        }

        if (!dataSet.has(projectId)) {
          dataSet.set(projectId, {
            id: projectId,
            title: ele.project.name,
            icon: baseInfo.icon,
            children: [baseInfo],
          });
        } else {
          dataSet.get(projectId).children.push(baseInfo);
        }
      } catch (error) {
        console.error(`handleTemplateData: 处理数据项 ${index} 时出错:`, error, ele);
      }
    });

    console.log('handleTemplateData 处理完成:', { count, dataSetSize: dataSet.size, dataSet });
    setNodeCount(count);
    setNodeMap(dataSet);
  };

  // 获取任务模板
  const updateTemplateList = () => {
    console.log('updateTemplateList: 开始获取模板数据');
    setLoading(true);
    setNodeCount(0);
    api
      .job_template_modelview()
      .then((res: any) => {
        console.log('updateTemplateList: API响应:', res);

        if (res?.status === 0 && res?.message === 'success') {
          const templateData = res?.result?.data;
          console.log('updateTemplateList: 模板数据:', templateData);

          if (templateData && Array.isArray(templateData)) {
            handleTemplateData(templateData);

            const currentTime = Date.now();
            const cacheData = {
              update: currentTime,
              value: templateData,
              expire: 1000 * 60 * 60 * 24, // 24小时更新一次
            };
            console.log('updateTemplateList: 保存缓存数据:', cacheData);
            storage.set('job_template', cacheData);
          } else {
            console.error('updateTemplateList: 模板数据格式错误:', templateData);
          }
        } else {
          console.error('updateTemplateList: API返回错误状态:', res);
        }
      })
      .catch((err) => {
        console.error('updateTemplateList: API请求失败:', err);
        if (err.response) {
          dispatch(updateErrMsg({ msg: err.response?.data?.message }));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    const jobTemplate = storage.get('job_template');

    console.log('ModuleTree useEffect - jobTemplate:', jobTemplate);

    // 检查缓存是否存在且有效
    if (
      jobTemplate &&
      jobTemplate.update &&
      jobTemplate.value &&
      Array.isArray(jobTemplate.value) &&
      Date.now() - jobTemplate.update < jobTemplate.expire
    ) {
      console.log('使用缓存数据，数据长度:', jobTemplate.value.length);
      try {
        handleTemplateData(jobTemplate.value);
      } catch (error) {
        console.error('处理缓存数据失败:', error);
        // 缓存数据有问题，重新获取
        updateTemplateList();
      }
    } else {
      console.log('缓存无效或过期，重新获取数据');
      updateTemplateList();
    }
  }, []);

  return (
    <Item shrink>
      <div className={show ? 'showModuleTree' : 'hideModuleTree'}>
        <div className="treeContainer">
          {/* 命令列表功能区域 */}
          <Stack horizontal verticalAlign="center" horizontalAlign="space-between" className="commandListHeader">
            <Text className="commandListTitle">命令列表</Text>
            <Stack horizontal className="commandListActions">
              <Image
                src={image_1750645938716_yy0iw7}
                className="commandListIcon"
                onClick={() => {
                  window.open('/frontend/train/train_template/job_template?isVisableAdd=true');
                }}
              ></Image>
              <Image
                src={image_1750645936565_tnjpyi}
                className="commandListIcon"
                onClick={() => {
                  if (!loading) {
                    updateTemplateList();
                  }
                }}
              ></Image>
            </Stack>
          </Stack>
          {/* 模板搜索 */}
          <div className="searchContainer">
            <div className="customSearchBox">
              <Image
                src={image_1750657959907_ie5fqt}
                style={{
                  width: '16px',
                  height: '16px',
                  marginLeft: '8px',
                  marginRight: '4px',
                }}
              />
              <input
                type="text"
                placeholder="搜索"
                className="searchInput"
                onChange={debounce((event) => {
                  const newValue = event.target.value;
                  if (!newValue) {
                    setShowSearch(false);
                    return;
                  }
                  console.log(newValue);
                  const temp = new Map();

                  nodeMap.forEach((value, key) => {
                    const { children } = value;

                    if (children.length > 0) {
                      children.forEach((element: any) => {
                        if (element?.name?.indexOf(newValue) > -1 || element?.describe?.indexOf(newValue) > -1) {
                          if (temp.has(key)) {
                            temp.set(key, temp.get(key).concat(element));
                          } else {
                            temp.set(key, [element]);
                          }
                        }
                      });
                    }
                  });
                  setSearchResult(temp);
                  setShowSearch(true);
                }, 300)}
                onBlur={() => {
                  setTimeout(() => {
                    setShowSearch(false);
                  }, 300);
                }}
              />
            </div>
            <div className="shortcutStyle">
              <Text className="shortcutText">⌘ K</Text>
            </div>
          </div>
          <Callout
            className="searchCallout"
            isBeakVisible={false}
            preventDismissOnLostFocus={true}
            hidden={!showSearch}
            calloutMaxHeight={300}
            target={'.searchContainer'}
          >
            <Stack className="searchListStyle">
              {Array.from(searchResult.keys()).length === 0 ? (
                <div
                  style={{
                    textAlign: 'center',
                  }}
                >
                  {t('暂无匹配')}
                </div>
              ) : (
                ''
              )}
              {Array.from(searchResult.keys()).map((key: any) => {
                const currentRes = searchResult.get(key);

                return (
                  <React.Fragment key={key}>
                    {currentRes?.map((cur: any) => {
                      return (
                        <SearchItem
                          key={cur.id}
                          model={cur}
                          onClick={() => {
                            console.log(expandNodes);
                            if (!expandNodes.has(key)) {
                              expandNodes.add(key);
                              setExpandNodes(new Set(expandNodes));
                            }
                          }}
                        />
                      );
                    })}
                  </React.Fragment>
                );
              })}
            </Stack>
          </Callout>

          {/* 模板列表 */}
          <div className="moduleTreeStyle">
            <div className="moduleTreeBody">
              {loading ? (
                <Stack className="spinnerContainer">
                  <Spinner size={SpinnerSize.large} label="Loading" />
                </Stack>
              ) : (
                <ul className="moduleListStyle">
                  {Array.from(nodeMap.keys()).map((key: any) => {
                    const curNode = nodeMap.get(key);
                    return (
                      <li key={key} className="moduleListItem">
                        <div
                          role="button"
                          onClick={() => {
                            if (expandNodes.has(key)) {
                              expandNodes.delete(key);
                            } else {
                              expandNodes.add(key);
                            }
                            setExpandNodes(new Set(expandNodes));
                          }}
                        >
                          <Stack horizontal className="itemFolderNode">
                            <Stack.Item styles={{ root: { display: 'flex', alignItems: 'center', gridGap: '8px' } }}>
                              <Image src={curNode.icon}></Image>
                              {curNode.title}
                            </Stack.Item>
                            <Stack.Item>
                              <Image src={expandNodes.has(key) ? image_1750237685965_xs2mln : image_1750237692610_nidqd3}></Image>
                            </Stack.Item>
                          </Stack>
                        </div>
                        {expandNodes.has(key) ? (
                          <ul role="group" style={{ paddingLeft: '0px' }}>
                            {curNode.children?.map((cur: any) => {
                              return (
                                <li className="moduleListItem" key={cur.id}>
                                  <div role="button">
                                    <ModuleItem model={cur}></ModuleItem>
                                  </div>
                                </li>
                              );
                            })}
                          </ul>
                        ) : null}
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </Item>
  );
};

export default ModuleTree;
