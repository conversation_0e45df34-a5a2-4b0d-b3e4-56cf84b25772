import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import cookie from 'cookie';
import { getI18n } from 'react-i18next';
import { message } from 'antd';
import { API_CONFIG, getAuthorizationHeader } from '../utils/config';

// 防重复提示的标记
let isShowingError = false;

const { myapp_username, t_uid, km_uid } = cookie.parse(document.cookie);
const Authorization = myapp_username || t_uid || km_uid || '';

// console.log(getI18n())

axios.defaults.baseURL = process.env.NODE_ENV === 'development' ? '' : process.env.REACT_APP_BASE_URL;

// axios.defaults.headers = Object.assign(axios.defaults.headers, {
//   'Content-Type': 'application/json',
//   // Authorization,
// });

axios.interceptors.response.use(
  (res: AxiosResponse) => res,
  (err) => Promise.reject(err),
);

interface IAxiosRequest {
  url: string;
  data?: any;
  options?: any;
}

/**
 * 获取默认的请求配置
 */
function getDefaultOptions(): AxiosRequestConfig {
  return {
    timeout: API_CONFIG.TIMEOUT,
    responseType: API_CONFIG.RESPONSE_TYPE,
    headers: {
      ...API_CONFIG.DEFAULT_HEADERS,
      language: getI18n().language,
      Authorization: getAuthorizationHeader(),
    },
  };
}

const Ajax = {
  get: function (url: string, options: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const defaultOptions = getDefaultOptions();
      Object.assign(defaultOptions, options);

      axios
        .get(url, defaultOptions)
        .then((response) => {
          resolve(response.data);
        })
        .catch((err) => {
          this._errHandle(err);
          reject(err);
        });
    });
  },

  delete: function (url: string, options: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const defaultOptions = getDefaultOptions();
      Object.assign(defaultOptions, options);

      axios
        .delete(url, defaultOptions)
        .then((response) => {
          resolve(response.data);
        })
        .catch((err) => {
          this._errHandle(err);
          reject(err);
        });
    });
  },

  post: function ({ url, data, options }: IAxiosRequest): Promise<any> {
    return new Promise((resolve, reject) => {
      const defaultOptions = getDefaultOptions();
      Object.assign(defaultOptions, options);

      axios
        .post(url, data, defaultOptions)
        .then((response) => {
          resolve(response.data);
        })
        .catch((err) => {
          this._errHandle(err);
          reject(err);
        });
    });
  },

  put: function ({ url, data, options }: IAxiosRequest): Promise<any> {
    return new Promise((resolve, reject) => {
      const defaultOptions = getDefaultOptions();
      Object.assign(defaultOptions, options);

      axios
        .put(url, data, defaultOptions)
        .then((response) => {
          resolve(response.data);
        })
        .catch((err) => {
          this._errHandle(err);
          reject(err);
        });
    });
  },

  _errHandle: function (error: AxiosError): void {
    // 防重复提示：如果正在显示错误，则不重复显示
    if (isShowingError) {
      console.error('Error:', error);
      return;
    }

    isShowingError = true;

    // 3秒后重置标记，允许下次显示错误
    setTimeout(() => {
      isShowingError = false;
    }, 3000);

    if (error.response) {
      // 处理服务器响应错误
      const { status, data } = error.response;

      if (status === 401) {
        // 401 未授权错误
        message.warning('登录已过期，请重新登录');
        // 延迟跳转，给用户时间看到提示
        setTimeout(() => {
          // 根据环境区分端口号
          const protocol = window.location.protocol;
          const hostname = window.location.hostname;
          const port = process.env.NODE_ENV === 'development' ? 3333 : window.location.port;
          window.location.href = `${protocol}//${hostname}:${port}/frontend/login`;
        }, 1000);
      } else if (status >= 500) {
        // 服务器错误
        message.error('服务器错误，请稍后重试');
      } else if (status >= 400) {
        // 客户端错误
        const errorMsg = data?.message || data?.error || `请求失败 (${status})`;
        message.error(errorMsg);
      } else {
        message.error('网络请求失败');
      }

      console.error('Response Error:', error.response);
    } else if (error.request) {
      // 网络请求错误（没有收到响应）
      message.error('网络连接失败，请检查网络设置');
      console.error('Request Error:', error.request);
    } else {
      // 其他错误
      message.error(`请求错误: ${error.message}`);
      console.error('Error:', error.message);
    }

    console.error('Error Config:', error.config);
  },
};

export default Ajax;
