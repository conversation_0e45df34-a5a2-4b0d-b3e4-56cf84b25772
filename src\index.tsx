import React from 'react';
import ReactDOM from 'react-dom';
import { HashRouter as Router } from 'react-router-dom';
import {
  mergeStyles,
  initializeIcons,
  createTheme,
  fontFace,
  createFontStyles,
  registerIcons,
  ThemeProvider,
  registerDefaultFontFaces,
} from '@fluentui/react';
import AppRouter from './routes';
import { store } from './models/store';
import { Provider } from 'react-redux';
// import i18n from './locales/i18n'
import './app.less';

import './locales/i18n';
// i18n.changeLanguage('en')

const isDev = process.env.NODE_ENV === 'development' ? true : false;
const assetsUrl = isDev ? '/assets' : '/static/appbuilder/assets';

window.FabricConfig = {
  iconBaseUrl: `${assetsUrl}/fonts/`,
  fontBaseUrl: `${assetsUrl}`,
};

// fluentui icon 资源初始化
initializeIcons(`${assetsUrl}/fonts/`);
registerDefaultFontFaces(`${assetsUrl}`);

const myTheme = createTheme({
  palette: {
    themePrimary: '#006BFF',
    themeLighterAlt: '#f0f7ff',
    themeLighter: '#d6e7ff',
    themeLight: '#a8cfff',
    themeTertiary: '#5fa3ff',
    themeSecondary: '#217dff',
    themeDarkAlt: '#005fe6',
    themeDark: '#0051c2',
    themeDarker: '#003b8c',
    neutralLighterAlt: '#faf9f8',
    neutralLighter: '#f3f2f1',
    neutralLight: '#edebe9',
    neutralQuaternaryAlt: '#e1dfdd',
    neutralQuaternary: '#d0d0d0',
    neutralTertiaryAlt: '#c8c6c4',
    neutralTertiary: '#595959',
    neutralSecondary: '#373737',
    neutralPrimaryAlt: '#2f2f2f',
    neutralPrimary: '#000000',
    neutralDark: '#151515',
    black: '#0b0b0b',
    white: '#ffffff',
  },
});
// 全局样式
mergeStyles({
  ':global(body,html,#app)': {
    margin: 0,
    padding: 0,
    height: '100vh',
    background: 'linear-gradient(180deg, #E6F4FF 0%, #F4F5F8 61.75%)',
  },
  ':global(.react-flow__edge-path)': {
    strokeWidth: '2 !important',
  },
  ':global(.ant-tabs-top > .ant-tabs-nav::before)': {
    borderBottom: 'none !important',
  },
});

// eslint-disable-next-line react/no-deprecated
ReactDOM.render(
  <ThemeProvider style={{ height: '100vh' }} applyTo="body" theme={myTheme}>
    <React.StrictMode>
      <Provider store={store}>
        <Router>
          <AppRouter />
        </Router>
      </Provider>
    </React.StrictMode>
  </ThemeProvider>,
  document.getElementById('app'),
);
