import React, { useState, useEffect } from 'react';
import { Stack, IStackStyles, Text, MessageBar, MessageBarType } from '@fluentui/react';
import { Node, Edge } from 'react-flow-renderer';
import TopNav from '@src/components/TopNav/index';
import EditorAce from '@src/components/EditorAce/index';
import FlowEditor from '@src/components/FlowEditor/index';
import ModuleTree from '@src/components/ModuleTree/index';
import Monitor from '@src/components/Monitor/index';
import Report from '@src/components/Report/index';
import { hasUrlParam, getUrlParam } from '@src/utils/urlParser';
import { useAppSelector, useAppDispatch } from '@src/models/hooks';
import { selectPipelineParams, selectPipelineId, getPipeline, selectInfo } from '@src/models/pipeline';
import { updateElements } from '@src/models/element';
import { updateRunProgress } from '@src/models/task';
import api from '@src/api';
import { isNode, isEdge } from 'react-flow-renderer';
import image_1718702934218_jqz6lu from '../public/assets/images/image_1718702934218_jqz6lu.svg';
import image_1718702934218_fayvt7 from '../public/assets/images/image_1718702934218_fayvt7.svg';
import image_1718702934217_i5x3bh from '../public/assets/images/image_1718702934217_i5x3bh.svg';
import image_1718702934217_kqu0mg from '../public/assets/images/image_1718702934217_kqu0mg.svg';
import image_1718702934216_1kw9je from '../public/assets/images/image_1718702934216_1kw9je.svg';
import image_1718702934216_tysn1v from '../public/assets/images/image_1718702934216_tysn1v.svg';
import image_1718702934215_g4nuwp from '../public/assets/images/image_1718702934215_g4nuwp.svg';
import image_1718702934215_a93jyd from '../public/assets/images/image_1718702934215_a93jyd.svg';
import image_1718702934214_u6b34x from '../public/assets/images/image_1718702934214_u6b34x.svg';
import image_1718702934214_hz0nqf from '../public/assets/images/image_1718702934214_hz0nqf.svg';
import image_1718702934213_8cq7dn from '../public/assets/images/image_1718702934213_8cq7dn.svg';
import image_1718702934213_ozr5lv from '../public/assets/images/image_1718702934213_ozr5lv.svg';
import image_1718702934212_d9tqbp from '../public/assets/images/image_1718702934212_d9tqbp.svg';
import image_1718702934212_m0a72s from '../public/assets/images/image_1718702934212_m0a72s.svg';
import image_1718702934211_3xqz9u from '../public/assets/images/image_1718702934211_3xqz9u.svg';

const randomImage = [
  { id: 1, image: image_1718702934218_jqz6lu },
  { id: 2, image: image_1718702934218_fayvt7 },
  { id: 3, image: image_1718702934217_i5x3bh },
  { id: 4, image: image_1718702934217_kqu0mg },
  { id: 5, image: image_1718702934216_1kw9je },
  { id: 6, image: image_1718702934216_tysn1v },
  { id: 7, image: image_1718702934215_g4nuwp },
  { id: 8, image: image_1718702934215_a93jyd },
  { id: 9, image: image_1718702934214_u6b34x },
  { id: 10, image: image_1718702934214_hz0nqf },
  { id: 11, image: image_1718702934213_8cq7dn },
  { id: 12, image: image_1718702934213_ozr5lv },
  { id: 13, image: image_1718702934212_d9tqbp },
  { id: 14, image: image_1718702934212_m0a72s },
  { id: 15, image: image_1718702934211_3xqz9u },
];

// app 页面初始化样式
const appContainerStyle: IStackStyles = {
  root: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
};
const bodyContainerStyle: IStackStyles = {
  root: {
    height: 'calc(100% - 56px)',
  },
};

const App: React.FC = () => {
  const [key, setKey] = useState('debug'); // 默认选中 debug，可根据需要修改
  const [reportUrl, setReportUrl] = useState<string>('');
  const pipelineParamsStr = useAppSelector(selectPipelineParams);
  const pipelineId = useAppSelector(selectPipelineId);
  const pipelineInfo = useAppSelector(selectInfo);
  const dispatch = useAppDispatch();

  // 全局初始化 pipeline 和 elements，保证Monitor等tab也能拿到数据
  useEffect(() => {
    if (pipelineId) {
      dispatch(getPipeline());
    }
  }, [pipelineId, dispatch]);

  // 获取运行进度数据
  useEffect(() => {
    if (pipelineInfo?.pipeline_argo_id) {
      api.pipeline_modelview_run_progress(pipelineInfo?.pipeline_argo_id).then((res) => {
        const { status, result } = res;
        if (status === 0) {
          dispatch(updateRunProgress(result));
        }
      });
    }
  }, [pipelineInfo?.pipeline_argo_id, dispatch]);

  // 全局解析 expand 并同步 elements 到 redux
  useEffect(() => {
    const expand = JSON.parse(pipelineInfo?.expand || '[]');
    const elements = expand.map((ele: Node | Edge) => {
      // 设置节点icon
      if (isNode(ele)) {
        return { ...ele, data: { ...ele.data, icon: randomImage[Math.floor(Math.random() * randomImage.length)].image } };
      }
      if (isEdge(ele) && !ele?.arrowHeadType) {
        return Object.assign(ele, { arrowHeadType: 'arrow' });
      }
      if (isNode(ele) && !ele.data.label) {
        ele.data.label = ele.data.name;
      }
      return ele;
    });

    dispatch(updateElements(elements));
  }, [pipelineInfo]);

  useEffect(() => {
    // 解析pipelineParamsStr中的参数
    const pipelineParamsObj = (() => {
      if (!pipelineParamsStr) {
        return {};
      }

      try {
        return JSON.parse(decodeURIComponent(pipelineParamsStr));
      } catch (error) {
        console.warn('解析pipelineParamsStr失败:', error);
        return {};
      }
    })();

    // 检查是否有report参数，如果有就跳转到报告
    if (pipelineParamsObj.enter_page === 'report') {
      setReportUrl(pipelineParamsObj.report);
      setKey('report');
    }
    // 检查是否有日志参数，如果有就跳转到监控（日志）
    else if (pipelineParamsObj.enter_page === 'log') {
      setKey('monitor');
    }
  }, [pipelineParamsStr]);

  // 监听自定义事件，用于从DataSet组件切换到监控页面
  useEffect(() => {
    const handleSwitchToMonitor = () => {
      // 直接切换到监控页面
      setKey('monitor');
    };

    // 添加事件监听器
    window.addEventListener('switchToMonitor', handleSwitchToMonitor);

    // 清理事件监听器
    return () => {
      window.removeEventListener('switchToMonitor', handleSwitchToMonitor);
    };
  }, []);

  return (
    <Stack className="app-container" styles={appContainerStyle}>
      {/* 顶部导航栏 */}
      <TopNav
        activeKey={key}
        onTabChange={(key) => {
          console.log('当前选中tab:', key);
          setKey(key);
        }}
      />
      {key === 'report' && <Report reportUrl={reportUrl} onTabChange={setKey} />}
      {key === 'monitor' && <Monitor />}
      {key === 'debug' && (
        <Stack horizontal styles={bodyContainerStyle}>
          {/* 任务模板库 */}
          <ModuleTree />
          {/* 流水线编辑 */}
          <FlowEditor />
          {/* JSON 编辑器 */}
          <EditorAce />
        </Stack>
      )}
    </Stack>
  );
};

export default App;
