interface DataPoint {
  x: number;
  y: number;
}

interface LegendData {
  name: string;
  value: string;
  step: string;
  relative: string;
}

/**
 * 从数组创建多个数据点的辅助函数
 * @param steps 步数数组
 * @param values 数值数组
 * @param name 名称
 * @returns 数据点和图例数据
 */
const createMultipleDataPoints = (steps: number[], values: number[], name: string) => {
  if (!steps || !values || steps.length !== values.length) {
    return {
      data: [],
      legend: {
        name,
        value: '-',
        step: '0',
        relative: '0%',
      },
    };
  }

  const data = steps.map((step, index) => ({ x: step, y: values[index] }));
  const lastValue = values[values.length - 1];
  const firstValue = values[0];
  const relative = firstValue !== 0 ? (((lastValue - firstValue) / firstValue) * 100).toFixed(1) : '0';

  return {
    data,
    legend: {
      name,
      value: lastValue.toFixed(4),
      step: steps.length.toString(),
      relative: `${relative}%`,
    },
  };
};

/**
 * 生成验证损失曲线数据
 * @param modelResults 模型结果数据
 * @returns 单曲线数据对象
 */
export const generateValidationLossCurve = (modelResults: any) => {
  if (!modelResults) {
    return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
  }

  // modelResults 现在直接是 model_evaluation 对象
  if (modelResults['val/val_loss'] && modelResults['val/val_loss'].steps && modelResults['val/val_loss'].values) {
    const steps = modelResults['val/val_loss'].steps;
    const values = modelResults['val/val_loss'].values;
    return createMultipleDataPoints(steps, values, 'val_loss');
  }

  return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
};

/**
 * 生成训练损失曲线数据（按epoch）
 * @param modelResults 模型结果数据
 * @returns 单曲线数据对象
 */
export const generateTrainLossCurve = (modelResults: any) => {
  if (!modelResults) {
    return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
  }

  // modelResults 现在直接是 model_evaluation 对象
  if (
    modelResults['train/train_loss_epoch'] &&
    modelResults['train/train_loss_epoch'].steps &&
    modelResults['train/train_loss_epoch'].values
  ) {
    const steps = modelResults['train/train_loss_epoch'].steps;
    const values = modelResults['train/train_loss_epoch'].values;
    return createMultipleDataPoints(steps, values, 'train_loss_epoch');
  }

  return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
};

/**
 * 生成训练损失曲线数据（按step）
 * @param modelResults 模型结果数据
 * @returns 单曲线数据对象
 */
export const generateTrainLossStepCurve = (modelResults: any) => {
  if (!modelResults) {
    return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
  }

  // modelResults 现在直接是 model_evaluation 对象
  if (
    modelResults['train/train_loss_step'] &&
    modelResults['train/train_loss_step'].steps &&
    modelResults['train/train_loss_step'].values
  ) {
    const steps = modelResults['train/train_loss_step'].steps;
    const values = modelResults['train/train_loss_step'].values;
    return createMultipleDataPoints(steps, values, 'train_loss_step');
  }

  return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
};

/**
 * 生成测试损失曲线数据
 * @param modelResults 模型结果数据
 * @returns 单曲线数据对象
 */
export const generateTestLossCurve = (modelResults: any) => {
  if (!modelResults) {
    return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
  }

  // modelResults 现在直接是 model_evaluation 对象
  if (modelResults['test/test_loss'] && modelResults['test/test_loss'].steps && modelResults['test/test_loss'].values) {
    const steps = modelResults['test/test_loss'].steps;
    const values = modelResults['test/test_loss'].values;
    return createMultipleDataPoints(steps, values, 'test_loss');
  }

  return { data: [], legend: { name: '', value: '', step: '', relative: '' } };
};
