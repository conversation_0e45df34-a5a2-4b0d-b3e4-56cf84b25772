import React from 'react';
// import ObjectType from '../BaseTypes/ObjectType';
// import style from './style';

const VisualizedData: React.FC = () => {
  // const [data, setData] = useState({});
  // const [text, setText] = useState('0');

  return (
    <div className="visualized-data">
      {/* {text.length > 0 ? (
        <ObjectType data={data} />
      ) : (
        <div className={style.noDataStyle}>No data</div>
      )} */}
    </div>
  );
};

export default VisualizedData;
