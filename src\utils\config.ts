/**
 * 应用配置常量
 */

// JWT Token 配置
export const JWT_CONFIG = {
  // 默认的JWT token（开发环境使用）
  DEFAULT_TOKEN:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjdWJlLXN0dWRpbyIsInN1YiI6ImFkbWluIiwiaWF0IjoxNzUzNDA3NzI2LCJleHAiOjE3NTM0OTQxMjYsInVzZXJfaWQiOjEsInJvbGVzIjpbIkFkbWluIl19.UHWOAniL0Y9AVaz6wCnzJ0R3H_kWz2uk-FypDK_2hUA',
  // localStorage中存储token的key
  TOKEN_KEY: 'myapp_token',
} as const;

// API 配置
export const API_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: 10000,
  // 响应类型
  RESPONSE_TYPE: 'json',
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

/**
 * 获取Authorization header的值
 * 优先从localStorage获取，如果没有则使用默认token
 */
export function getAuthorizationHeader(): string {
  return localStorage.getItem(JWT_CONFIG.TOKEN_KEY) || JWT_CONFIG.DEFAULT_TOKEN;
}

/**
 * 设置token到localStorage
 */
export function setToken(token: string): void {
  localStorage.setItem(JWT_CONFIG.TOKEN_KEY, token);
}

/**
 * 清除token
 */
export function clearToken(): void {
  localStorage.removeItem(JWT_CONFIG.TOKEN_KEY);
}
