import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Typography, Row, Col, Modal, message as Message, Image } from 'antd';
import { CloseOutlined, FileOutlined } from '@ant-design/icons';
import { saveTaskList, selectRunProgress } from '@src/models/task';
import { savePipeline } from '@src/models/pipeline';
import api from '@src/api';
import { useAppDispatch, useAppSelector } from '@src/models/hooks';
import { selectInfo } from '@src/models/pipeline';
import { selectElements } from '@src/models/element';
import './index.less';
import image_1751362749196_landpy from '../../../../../../public/assets/images/image_1751362749196_landpy.svg';
import image_1751362750196_sgu88u from '../../../../../../public/assets/images/image_1751362750196_sgu88u.svg';
const { Title } = Typography;

// 递归统计所有节点数量和已完成数量
function countNodesAndSucceeded(node: any): { total: number; succeeded: number } {
  let total = 1;
  let succeeded = node.status?.label === 'Succeeded' ? 1 : 0;
  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      const childResult = countNodesAndSucceeded(child);
      total += childResult.total;
      succeeded += childResult.succeeded;
    }
  }
  return { total, succeeded };
}

const RunProgressModal: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const elements = useAppSelector(selectElements);
  const dispatch = useAppDispatch();
  const pipelineInfo = useAppSelector(selectInfo);
  const runProgress = useAppSelector(selectRunProgress);

  useEffect(() => {
    if (pipelineInfo?.pipeline_argo_id) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  }, [pipelineInfo?.pipeline_argo_id]);

  return (
    <>
      {visible && (
        <div className="run-progress-modal-custom">
          <Button className="run-progress-modal-close" type="text" icon={<CloseOutlined />} onClick={() => setVisible(false)} />
          <Title level={5} className="run-progress-modal-title">
            运行进度
          </Title>
          {/* 动态渲染进度条 */}
          {(() => {
            const dagRoot = runProgress?.dag?.[0];
            const { total, succeeded } = dagRoot ? countNodesAndSucceeded(dagRoot) : { total: 0, succeeded: 0 };
            const elementsWithId = elements.filter((el) => el && el.type === 'dataSet');
            const extraCount = elementsWithId.length;
            const realExtraCount = total > 0 && extraCount > 0 ? extraCount - 1 : extraCount;

            return (
              <Row className="run-progress-modal-checks" justify="space-between" align="middle" gutter={0}>
                {/* 原有进度节点 */}
                {[...Array(total)].map((_, i) => (
                  <React.Fragment key={`dag-${i}`}>
                    <Col>
                      <div className={`run-progress-modal-check-icon ${i < succeeded ? '' : 'run-progress-modal-check-icon-error'}`}>
                        <Image src={i < succeeded ? image_1751362750196_sgu88u : image_1751362749196_landpy} preview={false} />
                      </div>
                    </Col>
                    {i < total - 1 && (
                      <Col style={{ flex: '1' }}>
                        <div className="run-progress-modal-check-line" />
                      </Col>
                    )}
                  </React.Fragment>
                ))}
                {/* 分隔线 */}
                {extraCount > 0 && total > 0 && extraCount > 1 && (
                  <Col style={{ flex: '1' }}>
                    <div className="run-progress-modal-check-line" />
                  </Col>
                )}
                {/* 新增平铺节点 */}
                {[...Array(realExtraCount)].map((_, i) => (
                  <React.Fragment key={`extra-${i}`}>
                    <Col>
                      <div className="run-progress-modal-check-icon run-progress-modal-check-icon-default"></div>
                    </Col>
                    {i < realExtraCount - 1 && (
                      <Col style={{ flex: '1' }}>
                        <div className="run-progress-modal-check-line" />
                      </Col>
                    )}
                  </React.Fragment>
                ))}
              </Row>
            );
          })()}
          <div className="run-progress-modal-analysis">
            <b>分析：</b>
            {runProgress?.layout?.status}
          </div>
          {/* <Button type="primary" className="run-progress-modal-btn" block>
            查看完整报告
          </Button> */}
          <div className="run-progress-modal-btn-group">
            <Button icon={<FileOutlined />} className="run-progress-modal-btn-file" title="日志" />
            <Button
              type="primary"
              className="run-progress-modal-btn-retry"
              onClick={async () => {
                Modal.confirm({
                  title: '确定要重新运行吗？',
                  okText: '确定',
                  cancelText: '取消',
                  onOk: async () => {
                    await dispatch(await saveTaskList());
                    dispatch(savePipeline());
                    api.pipeline_modelview_run(pipelineInfo?.id).then((res) => {
                      const { status, message } = res;
                      if (status === 0) {
                        Message.success(message);
                      } else {
                        Message.error(message);
                      }
                    });
                  },
                });
              }}
            >
              重新运行
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default RunProgressModal;
