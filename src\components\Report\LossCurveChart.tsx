import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';

interface DataPoint {
  x: number;
  y: number;
}

interface LegendData {
  name: string;
  value: string;
  step: string;
  relative: string;
}

interface LossCurveChartProps {
  data: DataPoint[];
  legend: LegendData;
  title: string;
  yAxisFormat?: (d: number | { valueOf(): number }) => string;
  width?: number;
  height?: number;
  marginTop?: number;
  marginRight?: number;
  marginBottom?: number;
  marginLeft?: number;
}

const LossCurveChart: React.FC<LossCurveChartProps> = ({
  data,
  legend,
  title,
  yAxisFormat,
  width = 350,
  height = 280,
  marginTop = 35,
  marginRight = 15,
  marginBottom = 60, // 增加底部边距给图例留空间
  marginLeft = 35,
}) => {
  const svgRef = useRef<SVGSVGElement | null>(null);

  useEffect(() => {
    if (data && data.length > 0 && svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.selectAll('*').remove(); // 清除之前的内容

      // 设置比例尺
      const xExtent = d3.extent(data, (d) => d.x) as [number, number];
      const yExtent = d3.extent(data, (d) => d.y) as [number, number];

      // 处理单个数据点的情况
      let xDomain = xExtent;
      let yDomain = yExtent;

      if (data.length === 1) {
        // 对于单个数据点，创建一个合理的域范围
        const xValue = data[0].x;
        const yValue = data[0].y;
        xDomain = [Math.max(0, xValue - 1), xValue + 1];
        yDomain = [Math.max(0, yValue * 0.9), yValue * 1.1];
      }

      const xScale = d3
        .scaleLinear()
        .domain(xDomain)
        .range([marginLeft, width - marginRight]);

      const yScale = d3
        .scaleLinear()
        .domain(yDomain)
        .nice()
        .range([height - marginBottom, marginTop]);

      // 创建线条生成器
      const line = d3
        .line<DataPoint>()
        .x((d) => xScale(d.x))
        .y((d) => yScale(d.y))
        .curve(d3.curveMonotoneX); // 使用平滑曲线

      // 添加Y轴网格线
      svg
        .append('g')
        .attr('class', 'grid')
        .attr('transform', `translate(${marginLeft},0)`)
        .call(
          d3
            .axisLeft(yScale)
            .tickSize(-(width - marginLeft - marginRight))
            .tickFormat(() => ''),
        )
        .style('stroke', '#f0f0f0')
        .style('stroke-width', '0.5px')
        .style('opacity', 0.8);

      // 添加X轴网格线
      svg
        .append('g')
        .attr('class', 'grid')
        .attr('transform', `translate(0,${height - marginBottom})`)
        .call(
          d3
            .axisBottom(xScale)
            .tickSize(-(height - marginTop - marginBottom))
            .tickFormat(() => ''),
        )
        .style('stroke', '#f0f0f0')
        .style('stroke-width', '0.5px')
        .style('opacity', 0.8);

      // 添加线条或点
      if (data.length > 1) {
        // 多个数据点时绘制线条
        svg.append('path').datum(data).attr('fill', 'none').attr('stroke', '#006BFF').attr('stroke-width', 2).attr('d', line);
      } else if (data.length === 1) {
        // 单个数据点时绘制圆点
        svg.append('circle').attr('cx', xScale(data[0].x)).attr('cy', yScale(data[0].y)).attr('r', 5).attr('fill', '#006BFF');
      }

      // 添加坐标轴
      svg
        .append('g')
        .attr('transform', `translate(0,${height - marginBottom})`)
        .call(d3.axisBottom(xScale))
        .style('font-size', '11px')
        .selectAll('text')
        .style('fill', '#666');

      const yAxis = d3.axisLeft(yScale);
      if (yAxisFormat) {
        yAxis.tickFormat(yAxisFormat);
      }

      svg
        .append('g')
        .attr('transform', `translate(${marginLeft},0)`)
        .call(yAxis)
        .style('font-size', '11px')
        .selectAll('text')
        .style('fill', '#666');

      // 添加标题 - 左上角
      svg
        .append('text')
        .attr('x', marginLeft - 30)
        .attr('y', 18)
        .attr('text-anchor', 'start')
        .style('font-size', '14px')
        .style('font-weight', '500')
        .style('fill', '#333')
        .text(title);

      // 添加图例 - 左下角布局
      const legendGroup = svg.append('g').attr('transform', `translate(${marginLeft}, ${height - marginBottom + 35})`);

      // 图例标题行
      const legendHeader = legendGroup.append('g');

      // Run 标题
      legendHeader
        .append('text')
        .attr('x', -30)
        .attr('y', 0)
        .text('Run')
        .style('font-size', '11px')
        .style('font-weight', '600')
        .style('fill', '#666');

      // Value 标题
      legendHeader
        .append('text')
        .attr('x', 130)
        .attr('y', 0)
        .text('Value')
        .style('font-size', '11px')
        .style('font-weight', '600')
        .style('fill', '#666');

      // Step 标题
      legendHeader
        .append('text')
        .attr('x', 210)
        .attr('y', 0)
        .text('Step')
        .style('font-size', '11px')
        .style('font-weight', '600')
        .style('fill', '#666');

      // Relative 标题
      legendHeader
        .append('text')
        .attr('x', 270)
        .attr('y', 0)
        .text('Relative')
        .style('font-size', '11px')
        .style('font-weight', '600')
        .style('fill', '#666');

      // 图例数据行
      const legendData = legendGroup.append('g').attr('transform', 'translate(0, 15)');

      // 图例圆点
      legendData.append('circle').attr('cx', -25).attr('cy', 0).attr('r', 3).style('fill', '#006BFF');

      // 图例名称
      legendData.append('text').attr('x', -18).attr('y', 4).text(legend.name).style('font-size', '11px').style('fill', '#333');

      // Value 数据
      legendData.append('text').attr('x', 130).attr('y', 4).text(legend.value).style('font-size', '11px').style('fill', '#333');

      // Step 数据
      legendData.append('text').attr('x', 210).attr('y', 4).text(legend.step).style('font-size', '11px').style('fill', '#333');

      // Relative 数据
      legendData.append('text').attr('x', 270).attr('y', 4).text(legend.relative).style('font-size', '11px').style('fill', '#333');

      // 坐标轴样式优化
      svg.selectAll('.domain').style('stroke', '#d9d9d9').style('stroke-width', '1px');

      svg.selectAll('.tick line').style('stroke', '#d9d9d9').style('stroke-width', '1px');
    }
  }, [data, legend, title, yAxisFormat, width, height, marginTop, marginRight, marginBottom, marginLeft]);

  return <svg ref={svgRef} width={width} height={height}></svg>;
};

export default LossCurveChart;
