// eslint-disable-next-line @typescript-eslint/no-var-requires
const { createProxyMiddleware } = require("http-proxy-middleware");

// https://create-react-app.dev/docs/proxying-api-requests-in-development/
module.exports = function (app) {
  app.use(
    ["**/api/**", "/myapp", "/tensorboard"],
    createProxyMiddleware({
      target: "http://************:80",
      changeOrigin: true,
    }),
  );
};
