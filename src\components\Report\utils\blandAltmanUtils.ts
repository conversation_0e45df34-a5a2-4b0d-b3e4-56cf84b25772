import { BlandAltmanPoint, BlandAltmanData } from '../BlandAltmanPanel';

/**
 * 时间序列数据接口
 */
interface TimeSeriesData {
  steps: number[];
  values: number[];
}

/**
 * 从时间序列数据生成Bland-Altman数据点
 * 比较两个时间序列在相同时间点的值
 */
const generateBlandAltmanFromTimeSeries = (series1: TimeSeriesData, series2: TimeSeriesData, maxPoints = 50): BlandAltmanPoint[] => {
  const data: BlandAltmanPoint[] = [];

  // 找到两个序列的公共时间点
  const commonSteps = series1.steps.filter((step) => series2.steps.includes(step));

  // 限制数据点数量，均匀采样
  const stepSize = Math.max(1, Math.floor(commonSteps.length / maxPoints));
  const sampledSteps = commonSteps.filter((_, index) => index % stepSize === 0);

  sampledSteps.forEach((step) => {
    const index1 = series1.steps.indexOf(step);
    const index2 = series2.steps.indexOf(step);

    if (index1 !== -1 && index2 !== -1) {
      const value1 = series1.values[index1];
      const value2 = series2.values[index2];

      const mean = (value1 + value2) / 2;
      const difference = value1 - value2;

      data.push({ mean, difference });
    }
  });

  return data;
};

/**
 * 从模型评估结果生成Bland-Altman数据
 * 基于时间序列数据生成比较图表
 * @param modelResults 模型结果数据，包含时间序列格式的指标数据
 * @returns BlandAltmanData对象
 */
export const generateBlandAltmanFromModelResults = (modelResults: any): BlandAltmanData => {
  if (!modelResults) {
    return {};
  }

  // 处理新的数据格式：直接从 modelResults 中获取时间序列数据
  // 数据格式：{ "val/val_loss": { steps: [...], values: [...] }, ... }
  const blandAltmanData: BlandAltmanData = {};

  // 训练集数据：比较训练损失和验证损失的时间序列
  const trainLossEpoch = modelResults['train/train_loss_epoch'];
  const valLoss = modelResults['val/val_loss'];

  if (trainLossEpoch?.steps && trainLossEpoch?.values && valLoss?.steps && valLoss?.values) {
    blandAltmanData.train = generateBlandAltmanFromTimeSeries(trainLossEpoch, valLoss, 50);
  }

  // 验证集数据：比较验证DICE和IOU指标的时间序列
  const valDice = modelResults['val/val_dice'];
  const valIou = modelResults['val/val_iou'];

  if (valDice?.steps && valDice?.values && valIou?.steps && valIou?.values) {
    blandAltmanData.validation = generateBlandAltmanFromTimeSeries(valDice, valIou, 50);
  }

  // 测试集数据：比较测试损失和验证损失
  const testLoss = modelResults['test/test_loss'];

  if (testLoss?.steps && testLoss?.values && valLoss?.steps && valLoss?.values) {
    blandAltmanData.test = generateBlandAltmanFromTimeSeries(testLoss, valLoss, 40);
  }

  return blandAltmanData;
};
