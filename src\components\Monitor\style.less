.monitor-page {
  width: 1400px;
  margin: 0 auto;
  height: calc(100% - 56px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.monitor-center-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  flex: 1;

  &.tensorboard-active {
    align-items: stretch;
    justify-content: stretch;
    min-height: auto;
    height: 100%;
  }
}
.monitor-icon-wrapper {
  position: relative;
  margin-bottom: 20px;

  &.tensorboard-wrapper {
    width: 100%;
    height: 100%;
    margin: 0;
    display: flex;
    flex-direction: column;
  }
}
.monitor-floating-card {
  width: 282px;
  height: 282px;
}
.monitor-card-title {
  position: absolute;
  left: 60%;
  top: 10%;
  width: 92px;
  height: 144px;
}

.monitor-center-btn {
  width: 158px;
  height: 40px;
}

.monitor-tensorboard-container {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

.monitor-tensorboard-iframe {
  width: 100%;
  height: 100%;
  flex: 1;
  border: none;
}

// Tabs 样式
.monitor-ant-tabs {
  .ant-tabs-content-holder {
    display: none; // 隐藏默认的 tab content，因为我们自己控制显示
  }
}
