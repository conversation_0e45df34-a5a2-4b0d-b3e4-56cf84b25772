.showModuleTree {
  width: 200px;
  height: 100%;
  transition: all 0.35s ease 0s;
  overflow-x: hidden;
  visibility: visible;
}
.hideModuleTree {
  width: 0px;
  height: 100%;
  transition: width 0.35s ease 0s;
  overflow-x: hidden;
  visibility: hidden;
}
.treeContainer {
  width: 200px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgb(234, 234, 234);
  background-color: #fff;
}
.searchContainer {
  position: relative;
  height: 28px;
  margin: 0 16px 8px 16px;
  display: flex;
  align-items: center;
  border-radius: 6px;
  background-color: #f5f7fa;
}
.customSearchBox {
  display: flex;
  align-items: center;
  flex-grow: 1;
}
.searchInput {
  width: calc(100% - 50px);
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 14px;
  padding: 8px 0;
  &::placeholder {
    color: rgba(0, 0, 0, 0.25);
    opacity: 1;
  }
}
.shortcutStyle {
  padding-right: 8px;
  line-height: 28px;
}
.shortcutText {
  font-size: 10px;
  color: rgba(0, 0, 0, 0.25);
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.searchCallout {
  .ms-Callout-main {
    overflow-y: overlay;
    will-change: transform;
    // 滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      min-height: 15px;
      border: 6px solid transparent;
      background-clip: padding-box;
      background-color: rgb(200, 200, 200);
    }
  }
}
.searchListStyle {
  width: 287px;
  padding: 8px 0;
}
.moduleTreeStyle {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  flex-grow: 1;
}
.moduleTreeBody {
  position: relative;
  height: 100%;
}
.listIconStyle {
  display: inline-flex;
  font-size: 12px;
  line-height: 12px;
  margin-right: 4px;
  height: 6px;
  color: rgb(55, 55, 55);
  user-select: none;
}
.spinnerContainer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.moduleListStyle {
  padding: 0 16px;
  margin: 0 auto;
  overflow-y: overlay;
  height: 100%;
  will-change: transform;
  // 滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    min-height: 15px;
    border: 6px solid transparent;
    background-clip: padding-box;
    background-color: rgb(200, 200, 200);
  }
}
.moduleListItem {
  list-style: none;
  outline: none;
}
.itemFolderNode {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 0 4px;
  border: 1px solid transparent;
  height: 44px;
  font-family: 'Segoe UI', sans-serif;
  font-size: 16px;
  line-height: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 8px;
  &:hover {
    background-color: #f5f7fa;
  }
}
.commandListHeader {
  width: 100%;
  padding: 8px 16px;
}
.commandListTitle {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}
.commandListActions {
  display: flex;
  gap: 4px;
}
.commandListIcon {
  width: 32px;
  height: 32px;
  cursor: pointer;
}
