import React from 'react';
import LossCurveChart from './LossCurveChart';
import './LossCurvePanel.less';

interface DataPoint {
  x: number;
  y: number;
}

interface LegendData {
  name: string;
  value: string;
  step: string;
  relative: string;
}

interface LossCurvePanelProps {
  data: {
    data: DataPoint[];
    legend: LegendData;
  };
  title?: string;
}

const LossCurvePanel: React.FC<LossCurvePanelProps> = ({ data, title = 'LOSS曲线' }) => {
  // 根据标题判断是否需要特殊的Y轴格式化
  const isLearningRate = title.toLowerCase().includes('learning_rate') || title.toLowerCase().includes('学习率');
  const yAxisFormat = isLearningRate
    ? (d: number | { valueOf(): number }) => {
        const value = typeof d === 'number' ? d : d.valueOf();
        if (value === 0) return '0';
        return value.toExponential(0);
      }
    : undefined;

  return (
    <div className="loss-curve-panel">
      <LossCurveChart data={data.data} legend={data.legend} title={title} yAxisFormat={yAxisFormat} width={350} height={280} />
    </div>
  );
};

export default LossCurvePanel;
