import { mergeStyles } from '@fluentui/react';

const baseContainerStyle = {
  height: 54,
  minWidth: 272,
  borderRadius: 100,
  borderStyle: 'solid',
  display: 'flex',
  flexDirection: 'row',
  backgroundColor: '#fff',
  fontSize: 12,
  cursor: 'move',
  boxSizing: 'border-box',
  transition: 'all 0.3s',
};
const nodeContainer = mergeStyles({
  ...baseContainerStyle,
  ...{
    borderWidth: 1,
    borderColor: '#b1b1b7',
    minHeight: 'auto',
    height: 'auto',
    flexDirection: 'column',
    borderRadius: '8px',
    padding: '8px',
  },
});
const nodeOnSelect = mergeStyles({
  ...baseContainerStyle,
  ...{
    borderWidth: 1,
    borderColor: '#006dce',
    backgroundColor: '#f1f7fd',
    minHeight: 'auto',
    height: 'auto',
    flexDirection: 'column',
    borderRadius: '8px',
    padding: '8px',
  },
});
const nodeBar = mergeStyles({
  width: 8,
  flexShrink: 0,
  borderRadius: '3px 0 0 3px',
  borderRight: '1px solid #8a8886',
  margin: '-8px 0 -8px -8px',
});
const nodeContent = mergeStyles({
  boxSizing: 'border-box',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
});
const nodeConentTitleBar = mergeStyles({
  display: 'flex',
  marginLeft: 7,
  minHeight: 26,
});
const nodeIconWrapper = mergeStyles({
  fontSize: 16,
  width: 64,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'rgb(0, 120, 212)',
  borderTopLeftRadius: 100,
  borderBottomLeftRadius: 100,
  margin: '-1px 0 -1px -1px',
});
const nodeIcon = mergeStyles({
  userSelect: 'none',
  boxSizing: 'border-box',
  color: '#fff',
  fontSize: 20,
  marginLeft: 8,
});
const nodeTitle = mergeStyles({
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  paddingLeft: 8,
  paddingBottom: 2,
  marginBottom: 2,
  fontWeight: 600,
  fontSize: 14,
  borderBottom: '1px dashed #c1c1c1',
  userSelect: 'none',
});
const handleStyle = mergeStyles({
  'width': '12px !important',
  'height': '12px !important',
  'bottom': '-7px !important',
  // top: '-7px !important',
  'borderColor': '#b1b1b7 !important',
  'backgroundColor': '#fff !important',
  'transition': 'all 0.3s',
  '&:hover': {
    borderWidth: '2px !important',
    borderColor: 'var(--hover-color) !important',
    cursor: 'pointer !important',
  },
});
const hidden = mergeStyles({
  visibility: 'hidden',
});

const nodeContentWrapper = mergeStyles({
  height: '100%',
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'column',
});

const nodeTips = mergeStyles({
  color: 'rgb(177, 177, 183)',
  paddingLeft: 8,
  maxWidth: '250px',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
});

// 操作按钮容器
const actionButtons = mergeStyles({
  display: 'flex',
  justifyContent: 'center',
  marginTop: '8px',
  paddingTop: '8px',
  borderTop: '1px dashed #e1e1e1',
});

// 配置按钮
const configButton = mergeStyles({
  'fontSize': '12px',
  'height': '24px',
  'padding': '0 8px',
  '&:hover': {
    backgroundColor: '#f0f0f0',
  },
});

// 参数容器
const parametersContainer = mergeStyles({
  marginTop: '12px',
  padding: '8px',
  backgroundColor: '#f8f9fa',
  borderRadius: '4px',
  border: '1px solid #e1e1e1',
});

// 参数组
const parameterGroup = mergeStyles({
  marginBottom: '16px',
  padding: '8px',
  backgroundColor: '#fff',
  borderRadius: '4px',
  border: '1px solid #e8e8e8',
});

// 参数组标题
const parameterGroupTitle = mergeStyles({
  fontSize: '12px',
  fontWeight: '600',
  color: '#333',
  marginBottom: '8px',
  display: 'block',
  borderBottom: '1px solid #e1e1e1',
  paddingBottom: '4px',
});

// 参数项
const parameterItem = mergeStyles({
  'marginBottom': '12px',
  'padding': '8px',
  'backgroundColor': '#fafafa',
  'borderRadius': '4px',
  'border': '1px solid #f0f0f0',
  '&:last-child': {
    marginBottom: '0',
  },
});

// 参数描述
const argsDescription = mergeStyles({
  'marginTop': '4px',
  'fontSize': '11px',
  'color': '#666',
  'lineHeight': '1.3',
  '& span': {
    fontSize: '11px',
  },
});

// 提示链接
const tipLink = mergeStyles({
  'fontWeight': 'bold',
  'color': '#0078d4',
  'marginLeft': '8px',
  'cursor': 'pointer',
  'fontSize': '11px',
  '&:hover': {
    textDecoration: 'underline',
  },
});

// 提示样式
const tipStyle = mergeStyles({
  'maxWidth': '250px',
  '& .cube-tip': {
    fontSize: '11px',
    lineHeight: '1.3',
  },
});

// 输入字段
const inputField = mergeStyles({
  'marginBottom': '8px',
  '& .ms-TextField-field': {
    fontSize: '11px',
  },
  '& .ms-TextField-label': {
    fontSize: '11px',
    fontWeight: '500',
  },
});

// 加载容器
const loadingContainer = mergeStyles({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '20px',
});

const loadingText = mergeStyles({
  fontSize: '12px',
  color: '#666',
});

export default {
  nodeTips,
  nodeContentWrapper,
  nodeContainer,
  nodeOnSelect,
  nodeBar,
  nodeContent,
  nodeConentTitleBar,
  nodeIconWrapper,
  nodeIcon,
  nodeTitle,
  handleStyle,
  hidden,
  actionButtons,
  configButton,
  parametersContainer,
  parameterGroup,
  parameterGroupTitle,
  parameterItem,
  argsDescription,
  tipLink,
  tipStyle,
  inputField,
  loadingContainer,
  loadingText,
};
