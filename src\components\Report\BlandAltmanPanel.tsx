import React from 'react';
import BlandAltmanChart, { BlandAltmanPoint } from './BlandAltmanChart';
import './BlandAltmanPanel.less';

export interface BlandAltmanData {
  train?: BlandAltmanPoint[];
  validation?: BlandAltmanPoint[];
  test?: BlandAltmanPoint[];
}

export type { BlandAltmanPoint };

interface BlandAltmanPanelProps {
  data: BlandAltmanData;
  title?: string;
}

const BlandAltmanPanel: React.FC<BlandAltmanPanelProps> = ({ data, title = 'Bland-Altman 图' }) => {
  // 获取第一个可用的数据集
  const chartData = data.train || data.validation || data.test || [];

  return (
    <div className="bland-altman-panel">
      <BlandAltmanChart data={chartData} title={title} width={350} height={280} />
    </div>
  );
};

export default BlandAltmanPanel;
