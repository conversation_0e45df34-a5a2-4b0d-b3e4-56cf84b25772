.loss-curve-panel {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 15px;

  // 确保SVG在容器中居中
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    display: block;
  }
}

// 优化LossCurveChart在LossCurvePanel中的样式
.loss-curve-panel {
  svg {
    .grid {
      stroke: #f0f0f0 !important;
      stroke-width: 0.5px !important;
      opacity: 0.8 !important;
    }

    text {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    // 线条样式
    path[stroke='#006BFF'] {
      stroke: #006bff !important;
      stroke-width: 2px !important;
    }

    // 坐标轴样式
    .domain {
      stroke: #d9d9d9 !important;
      stroke-width: 1px !important;
    }

    .tick {
      line {
        stroke: #d9d9d9 !important;
        stroke-width: 1px !important;
      }

      text {
        fill: #666 !important;
        font-size: 11px !important;
      }
    }

    // 标题样式
    text[text-anchor='start'] {
      fill: #333 !important;
      font-weight: 500 !important;
      font-size: 13px !important;
    }

    // 图例圆点
    circle {
      fill: #006bff !important;
    }

    // 图例文字样式
    text[font-weight='bold'] {
      fill: #333 !important;
      font-size: 11px !important;
    }

    text:not([font-weight='bold']):not([text-anchor='start']):not([text-anchor='middle']) {
      fill: #666 !important;
      font-size: 11px !important;
    }
  }
}

// 图表容器悬停效果
.loss-curve-panel {
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}
