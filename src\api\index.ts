import ajax from './ajax';
import { IPipelineAdd, IPipelineEdit } from '../types/pipeline';
import { ITaskAdd, ITaskEdit } from '../types/task';

// 获取任务模板列表
const job_template_modelview = (): Promise<any> => {
  return ajax.get('/job_template_modelview/api/', {
    params: {
      form_data: JSON.stringify({
        columns: [
          'project',
          'name',
          'version',
          'describe',
          'images',
          'workdir',
          'entrypoint',
          'args',
          'demo',
          'env',
          'hostAliases',
          'privileged',
          'accounts',
          'created_by',
          'changed_by',
          'created_on',
          'changed_on',
          'expand',
        ],
        str_related: 0,
      }),
    },
  });
};

const project_all = (): Promise<any> => {
  return ajax.get('/project_modelview/api/');
};
// 获取 org 项目组
const project_modelview = (): Promise<any> => {
  return ajax.get('/project_modelview/org/api/');
};

// 新增流水线
const pipeline_modelview_add = (data: IPipelineAdd): Promise<any> => {
  return ajax.post({ url: '/pipeline_modelview/api/', data });
};

// 获取流水线列表
const pipeline_modelview_demo = (): Promise<any> => {
  return ajax.get('/pipeline_modelview/api/demo/list/');
};

// 获取流水线列表
const pipeline_modelview_list = (): Promise<any> => {
  return ajax.get('/pipeline_modelview/api/my/list/');
};

const pipeline_modelview_all = (filters: string): Promise<any> => {
  return ajax.get(`/pipeline_modelview/home/<USER>/?form_data=${filters}`);
};

// 获取流水线信息
const pipeline_modelview_detail = (pipelineId: number | string): Promise<any> => {
  return ajax.get(`/pipeline_modelview/api/${pipelineId}`);
};

// 删除指定流水线
const pipeline_modelview_delete = (pipelineId: number | string): Promise<any> => {
  return ajax.delete(`/pipeline_modelview/api/${pipelineId}`);
};

// 流水线编辑提交
const pipeline_modelview_edit = (pipelineId: number | string, data: IPipelineEdit): Promise<any> => {
  return ajax.put({
    url: `/pipeline_modelview/api/${pipelineId}`,
    data,
  });
};

// 运行流水线
const pipeline_modelview_run = (pipelineId: number | string): Promise<any> => {
  return ajax.get(`/pipeline_modelview/api/run_pipeline/${pipelineId}`);
};

// 运行任务
const task_modelview_run = (taskId: number | string): Promise<any> => {
  return ajax.get(`/task_modelview/api/run/${taskId}`);
};

// 克隆流水线
const pipeline_modelview_copy = (pipelineId: number | string): Promise<any> => {
  return ajax.post({
    url: `/pipeline_modelview/api/copy_pipeline/${pipelineId}`,
  });
};

// 往流水线中添加task
const task_modelview_add = (pipelineId: number | string, data: ITaskAdd): Promise<any> => {
  return ajax.post({
    url: '/task_modelview/api/',
    data: {
      ...data,
      filters: [
        {
          col: 'pipeline',
          opr: 'rel_o_m',
          value: +pipelineId,
        },
      ],
    },
  });
};

// 获取流水线中相应的task
const task_modelview_get = (taskId: string | number): Promise<any> => {
  return ajax.get(`/task_modelview/api/${taskId}`);
};

// 删除对应的 task
const task_modelview_del = (taskId: string | number): Promise<any> => {
  return ajax.delete(`/task_modelview/api/${taskId}`);
};

// 清理对应的 task
const task_modelview_clear = (taskId: string | number): Promise<any> => {
  return ajax.get(`/task_modelview/api/clear/${taskId}`);
};

// 编辑 task
const task_modelview_edit = (pipelineId: string | number, taskId: string | number, data: ITaskEdit): Promise<any> => {
  return ajax.put({
    url: `/task_modelview/api/${taskId}`,
    data: {
      ...data,
      filters: [
        {
          col: 'pipeline',
          opr: 'rel_o_m',
          value: +pipelineId,
        },
      ],
    },
  });
};

// 获取tag列表
export const tag_modelview_list = (): Promise<any> => {
  return ajax.get('/tag/api/');
};
// 创建项目管理记录
const project_management_add = (data: any): Promise<any> => {
  return ajax.post({ url: '/project_management/api/', data });
};
// 更新项目管理记录
const project_management_update = (pipelineId: number | string, data: any): Promise<any> => {
  return ajax.put({ url: `/project_management/api/${pipelineId}`, data });
};
// 运行进度
const pipeline_modelview_run_progress = (pipeline_argo_id: number | string): Promise<any> => {
  return ajax.get(`/workflow_modelview/api/web/dag/dev/pipeline/${pipeline_argo_id}`);
};

// tensorboard smart_start
const tensorboard_smart_start = (data: { logdir: string; name: string; namespace: string; task_id: number | string }) => {
  return ajax.post({ url: '/tensorboard/smart_start', data });
};

const api = {
  job_template_modelview,
  project_all,
  project_modelview,
  pipeline_modelview_add,
  pipeline_modelview_demo,
  pipeline_modelview_list,
  pipeline_modelview_all,
  pipeline_modelview_detail,
  pipeline_modelview_delete,
  pipeline_modelview_edit,
  pipeline_modelview_run,
  pipeline_modelview_copy,
  task_modelview_add,
  task_modelview_get,
  task_modelview_del,
  task_modelview_clear,
  task_modelview_edit,
  tag_modelview_list,
  task_modelview_run,
  project_management_add,
  project_management_update,
  pipeline_modelview_run_progress,
  tensorboard_smart_start,
};

export default api;
