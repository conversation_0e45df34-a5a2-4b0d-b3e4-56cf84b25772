import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';

// Bland-Altman图的数据点接口
export interface BlandAltmanPoint {
  mean: number; // 两个测量值的均值 (x轴)
  difference: number; // 两个测量值的差值 (y轴)
}

// Bland-Altman图的统计信息
interface BlandAltmanStats {
  meanDifference: number; // 差值的均值
  stdDifference: number; // 差值的标准差
  upperLimit: number; // 上限 (mean + 1.96*std)
  lowerLimit: number; // 下限 (mean - 1.96*std)
}

export interface BlandAltmanChartProps {
  data: BlandAltmanPoint[];
  title: string;
  width?: number;
  height?: number;
  marginTop?: number;
  marginRight?: number;
  marginBottom?: number;
  marginLeft?: number;
}

// Bland-Altman图组件
const BlandAltmanChart: React.FC<BlandAltmanChartProps> = ({
  data,
  title,
  width = 280,
  height = 220,
  marginTop = 35,
  marginRight = 15,
  marginBottom = 35,
  marginLeft = 35,
}) => {
  const svgRef = useRef<SVGSVGElement | null>(null);

  // 计算统计信息
  const calculateStats = (data: BlandAltmanPoint[]): BlandAltmanStats => {
    const differences = data.map((d) => d.difference);
    const meanDifference = d3.mean(differences) || 0;
    const stdDifference = d3.deviation(differences) || 0;

    return {
      meanDifference,
      stdDifference,
      upperLimit: meanDifference + 1.96 * stdDifference,
      lowerLimit: meanDifference - 1.96 * stdDifference,
    };
  };

  useEffect(() => {
    if (data && data.length > 0 && svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.selectAll('*').remove(); // 清除之前的内容

      const stats = calculateStats(data);

      // 设置比例尺
      const xExtent = d3.extent(data, (d) => d.mean) as [number, number];
      const yExtent = d3.extent(data, (d) => d.difference) as [number, number];

      // 扩展y轴范围以包含统计线
      const yMin = Math.min(yExtent[0], stats.lowerLimit - 0.1);
      const yMax = Math.max(yExtent[1], stats.upperLimit + 0.1);

      const xScale = d3
        .scaleLinear()
        .domain(xExtent)
        .nice()
        .range([marginLeft, width - marginRight]);

      const yScale = d3
        .scaleLinear()
        .domain([yMin, yMax])
        .nice()
        .range([height - marginBottom, marginTop]);

      // 添加网格线
      svg
        .append('g')
        .attr('class', 'grid')
        .attr('transform', `translate(${marginLeft},0)`)
        .call(
          d3
            .axisLeft(yScale)
            .tickSize(-(width - marginLeft - marginRight))
            .tickFormat(() => ''),
        )
        .style('stroke-dasharray', '1,1')
        .style('opacity', 0.4);

      svg
        .append('g')
        .attr('class', 'grid')
        .attr('transform', `translate(0,${height - marginBottom})`)
        .call(
          d3
            .axisBottom(xScale)
            .tickSize(-(height - marginTop - marginBottom))
            .tickFormat(() => ''),
        )
        .style('stroke-dasharray', '1,1')
        .style('opacity', 0.4);

      // 添加统计参考线
      // 均值线 (橙色实线)
      svg
        .append('line')
        .attr('x1', marginLeft)
        .attr('x2', width - marginRight)
        .attr('y1', yScale(stats.meanDifference))
        .attr('y2', yScale(stats.meanDifference))
        .style('stroke', '#FF8D2F')
        .style('stroke-width', 2);

      // 上限线 (橙色虚线)
      svg
        .append('line')
        .attr('x1', marginLeft)
        .attr('x2', width - marginRight)
        .attr('y1', yScale(stats.upperLimit))
        .attr('y2', yScale(stats.upperLimit))
        .style('stroke', '#FF8D2F')
        .style('stroke-width', 1)
        .style('stroke-dasharray', '3,3');

      // 下限线 (橙色虚线)
      svg
        .append('line')
        .attr('x1', marginLeft)
        .attr('x2', width - marginRight)
        .attr('y1', yScale(stats.lowerLimit))
        .attr('y2', yScale(stats.lowerLimit))
        .style('stroke', '#FF8D2F')
        .style('stroke-width', 1)
        .style('stroke-dasharray', '3,3');

      // 添加散点 (蓝色)
      svg
        .selectAll('.dot')
        .data(data)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', (d) => xScale(d.mean))
        .attr('cy', (d) => yScale(d.difference))
        .attr('r', 2.5)
        .style('fill', '#006BFF')
        .style('opacity', 0.8);

      // 添加坐标轴
      svg
        .append('g')
        .attr('transform', `translate(0,${height - marginBottom})`)
        .call(d3.axisBottom(xScale))
        .style('font-size', '11px');

      svg.append('g').attr('transform', `translate(${marginLeft},0)`).call(d3.axisLeft(yScale)).style('font-size', '11px');

      // 添加轴标签
      svg
        .append('text')
        .attr('x', (width + marginLeft - marginRight) / 2)
        .attr('y', height - 8)
        .attr('text-anchor', 'middle')
        .style('font-size', '11px')
        .style('fill', '#666')
        .text('Mean');

      // 添加标题
      svg
        .append('text')
        .attr('x', marginLeft - 30)
        .attr('y', 18)
        .attr('text-anchor', 'start')
        .style('font-size', '14px')
        .style('font-weight', '500')
        .style('fill', '#333')
        .text(title);
    }
  }, [data, title, width, height, marginTop, marginRight, marginBottom, marginLeft]);

  return <svg ref={svgRef} width={width} height={height}></svg>;
};

export default BlandAltmanChart;
