.nodeContainer {
  height: 52px;
  width: 280px;
  max-width: 280px;
  min-width: 280px;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  font-size: 12px;
  cursor: move;
  box-sizing: border-box;
  transition: all 0.3s;
  min-height: auto;
  height: auto;
  flex-direction: column;
  border-radius: 12px;
  padding: 0 14px;
  border: 1px solid #fff;
}

.nodeOnSelect {
  height: 52px;
  width: 280px;
  max-width: 280px;
  min-width: 280px;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  font-size: 12px;
  cursor: move;
  box-sizing: border-box;
  transition: all 0.3s;
  min-height: auto;
  height: auto;
  flex-direction: column;
  border-radius: 12px;
  padding: 0 14px;
  border: 1px solid @primary-color;
}

.nodeIconWrapper {
  font-size: 16px;
  width: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(0, 120, 212);
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  margin: -1px 0 -1px -1px;
}

.nodeTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  span {
    max-width: 150px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }
}

.handleStyle {
  width: 3px !important;
  height: 10px !important;
  border: none !important;
  border-radius: unset !important;
  background: @primary-color !important;
}
.react-flow__handle-left {
  left: -2px !important;
}
.react-flow__handle-right {
  right: -2px !important;
}

.nodeContentWrapper {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.nodeTips {
  color: rgb(177, 177, 183);
  padding-left: 8px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.argsDescription {
  margin-top: 4px;
  font-size: 11px;
  color: #666;
  line-height: 1.3;
  span {
    font-size: 11px;
  }
}

.tipLink {
  font-weight: bold;
  color: #0078d4;
  margin-left: 8px;
  cursor: pointer;
  font-size: 11px;
  &:hover {
    text-decoration: underline;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 8px;
}

.loadingText {
  font-size: 12px;
  color: #666;
}

.parametersContainer {
  padding: 12px 0;

  .parameterGroup {
    margin-bottom: 16px;
    .parameterGroupTitle {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      margin-bottom: 4px;
    }
    .parameterItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #f5f7fa;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 2px;
      padding: 4px 8px;
      // 强制不换行
      .parameterItemLabel {
        width: 100px;
        text-align: left;
        padding-right: 4px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-input {
        flex: 1;
        border: none;
        border-radius: 6px;
        background-color: #f5f7fa;

        &:focus {
          background-color: #fff;
        }
      }
      .ant-select {
        flex: 1;
        .ant-select-selector {
          border-radius: 6px;
        }
      }
    }
  }
}

.nodeTitleBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 52px;
}

.iconGroup {
  display: flex;
  align-items: center;
  gap: 4px;
  .ant-image {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      border-radius: 4px;
      opacity: 0.8;
    }
  }
}
.ant-dropdown-menu {
  border-radius: 8px;
  padding: 8px;
  overflow: hidden;
}

.menuItem {
  width: 120px;
  height: 36px;
  padding: 4px 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  border-radius: 6px;
  transition: all 0.3s;
  &:hover {
    background-color: #f5f7fa;
  }
}
