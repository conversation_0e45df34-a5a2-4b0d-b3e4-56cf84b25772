/* eslint-disable @typescript-eslint/no-var-requires */
const path = require("path");
const CracoLessPlugin = require("craco-less");
const MonacoWebpackPlugin = require("monaco-editor-webpack-plugin");
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

module.exports = {
  devServer: {
    client: {
      overlay: false,
    },
  },
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            javascriptEnabled: true,
            modifyVars: { "@primary-color": "#006BFF" },
          },
        },
      },
    },
  ],
  webpack: {
    alias: {
      "@src": path.resolve(__dirname, "src"),
    },
    configure: (webpackConfig, { env, paths }) => {
      // 关闭 source map
      webpackConfig.devtool = false;
      // 插件注入
      webpackConfig.plugins.push(
        new MonacoWebpackPlugin({
          languages: ["json"],
        }),
      );
      // 如需分析包体积，取消下方注释
      // if (env !== 'production') {
      //   webpackConfig.plugins.push(new BundleAnalyzerPlugin());
      // }
      // 可选：修改打包输出路径
      // paths.appBuild = path.join(path.dirname(paths.appBuild), './build');
      return webpackConfig;
    },
  },
};
