.topNavContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  box-shadow: 0 2px 8px #f0f1f2;
  z-index: 10;
  .ant-tabs-tab {
    font-size: 16px;
    font-weight: 600;
  }
  .ant-tabs-tab + .ant-tabs-tab {
    margin-left: 62px !important;
  }
  .ant-tabs-ink-bar {
    height: 3px !important;
    border-radius: 12px;
  }
}

.topNavLeft {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 300px;
}

.topNavBack {
  cursor: pointer;
  transition: transform 0.2s;
  &:hover {
    transform: scale(1.1);
  }
}

.topNavTitle {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-left: 8px;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topNavEdit {
  margin-left: 8px;
  cursor: pointer;
  transition: color 0.2s;
  &:hover {
    color: #1890ff;
  }
}

.topNavTabs {
  flex: 1;
  display: flex;
  justify-content: center;
  .ant-tabs-nav {
    margin-bottom: 0;
  }
}

.topNavActions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  width: 300px;
  .ant-btn {
    min-width: 72px;
  }
  .topNavSave {
    background: rgba(0, 0, 0, 0.04);
    border: none;
    color: rgba(0, 0, 0, 0.85);
    border-radius: 8px;
    &:hover {
      background: rgba(0, 0, 0, 0.08);
    }
  }
  .topNavRun {
    border-radius: 8px;
    border: none;
  }
}
