// 页面背景
.report-page {
  width: 1400px;
  margin: 0 auto;
  padding: 20px 20px 120px 20px; // 底部增加120px空间给固定底栏
  height: calc(100% - 56px);
  overflow-y: auto;
}

.report-header {
  text-align: center;
  margin-bottom: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 12px;
  .header-left-line {
    width: 120px;
    height: 3px;
    background: linear-gradient(90deg, rgba(0, 107, 255, 0) 0%, #006bff 100%);
  }
  .header-right-line {
    width: 120px;
    height: 3px;
    background: linear-gradient(90deg, #006bff 0%, rgba(0, 107, 255, 0) 100%);
  }
  .header-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    letter-spacing: 2px;
  }
}

.report-main {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
}

.bottom-row {
  margin-top: 0;
}

.report-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.06);
  padding: 28px;
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  min-height: 260px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 12px;
  letter-spacing: 1px;
}

// 新版模型结果表格
.metrics-table-v2 {
  width: 100%;
  border-radius: 12px;
  background: #fff;
  box-shadow: none;
  .metrics-header,
  .metrics-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 60px;
  }
  .metrics-header {
    border-bottom: 1px solid #f5f7fa;
    .metrics-cell {
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
      .metrics-sub {
        display: inline-block;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        font-weight: 400;
        margin-left: 6px;
        vertical-align: middle;
        border-radius: 100px;
        border: 1px solid #e6eaf2;
        background: #f0f3f7;
        padding: 2px 8px;
      }
    }
  }
  .metrics-row {
    font-size: 17px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 600;
    border-bottom: 1px solid #f5f7fa;
    &:last-child {
      border-bottom: none;
    }
    .metrics-cell {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  .metrics-cell {
    flex: 1;
    text-align: center;
    &.header {
      flex: 1.2;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
    }
    &.label {
      flex: 1.2;
      text-align: left;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
    }
    .metrics-value {
      font-size: 24px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      min-width: 56px;
      display: inline-block;
      text-align: left;
    }
    .metrics-bar {
      display: inline-flex;
      align-items: center;
      gap: 2px;
      margin-left: 6px;
      height: 16px;
      i {
        display: inline-block;
        width: 4px;
        height: 12px;
        border-radius: 12px;
        background: #eee;
        opacity: 1;
      }
    }
  }
}

// 蓝色小圆点
.dot-blue {
  display: inline-block;
  width: 4px;
  height: 12px;
  border-radius: 12px;
  background: #006bff;
  margin-right: 8px;
}

// 数据信息卡片
.data-info-card {
  .data-info-blocks {
    display: flex;
    flex-wrap: wrap;
    gap: 18px 0;
    .data-info-block {
      width: 50%;
      margin-bottom: 10px;
      text-align: center;
      .data-info-value {
        font-size: 28px;
        font-weight: 600;
        color: #2563eb;
        margin-bottom: 2px;
      }
      .data-info-label {
        font-size: 14px;
        color: #7b8a9c;
        font-weight: 500;
      }
    }
  }
}

// 新版数据信息卡片
.data-info-cards {
  display: flex;
  flex-direction: row;
  gap: 12px;
  .data-info-item {
    height: 240px;
    background: #f5f7fa;
    border-radius: 12px;
    flex: 1;
    min-width: 0;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: flex-start;
    .data-info-main {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      gap: 2px;
    }
    .data-info-value {
      font-size: 24px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 1;
    }
    .data-info-unit {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      font-weight: 500;
      line-height: 1.2;
      position: relative;
      top: 2px;
    }
    .data-info-desc {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      font-weight: 500;
    }
    .data-info-divider {
      width: 100%;
      height: 1px;
      background: rgba(0, 0, 0, 0.08);
      margin: 10px 0;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}

// 数据配置（合并后的卡片）
.data-config-card {
  .data-config-content {
    display: flex;
    flex-direction: row;
    gap: 20px;

    .data-config-section {
      flex: 1;
      min-width: 0;

      .config-section-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 12px;
      }
    }
  }

  .data-preprocess-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
    height: 100%;
    .preprocess-item {
      background-color: #f5f7fa;
      border-radius: 12px;
      padding: 20px;
      flex: 1;
      .preprocess-value {
        font-size: 20px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }
      .preprocess-label {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 4px;
      }
    }
  }

  .augment-list {
    height: 100%;
    background-color: #f5f7fa;
    border-radius: 12px;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    .augment-item {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 600;
    }
  }
}

// 第三版模型训练参数卡片 - 表格样式
.model-param-card-v3 {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.06);
  padding: 24px 20px 20px 20px;
  min-width: 0;
  display: flex;
  flex-direction: column;

  .model-param-table {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .param-sections-row {
      display: flex;
      flex-direction: row;
      gap: 12px;

      .param-section {
        flex: 1;
        min-width: 0;
      }
    }

    .param-section {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 12px;
      }

      .param-row {
        display: flex;
        flex-direction: row;
        border-radius: 12px;
        overflow: hidden;

        .param-item {
          position: relative;
          background: #f5f7fa;
          flex: 1;
          min-width: 0;
          padding: 20px 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;

          .param-value {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
          }

          .param-label {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.45);
            margin-top: 4px;
          }
        }
        .param-item:not(:first-child):before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 40px;
          background: #e6eaf2;
        }
      }
    }
  }
}

// 底部操作栏 - 固定定位铺满全屏
.report-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 0 20px;
  height: 104px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  border-top: 1px solid #e6eaf2;
  background: #f5f7fa;
  .footer-btn-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 1400px;
    margin: 0 auto;
  }
  .footer-btn-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
  }
  .footer-btn-file {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 24px;
      height: 24px;
    }
  }

  .footer-btn {
    min-width: 160px;
    height: 48px;
    font-size: 16px;
    border-radius: 4px;
  }
  .export-btn {
    display: flex;
    align-items: center;
    justify-content: center;

    // 去除 hover 效果
    &:hover {
      background-color: inherit !important;
      border-color: inherit !important;
      color: inherit !important;
      box-shadow: none !important;
    }

    // 去除点击效果
    &:active,
    &:focus {
      background-color: inherit !important;
      border-color: inherit !important;
      color: inherit !important;
      box-shadow: none !important;
      transform: none !important;
    }

    img {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
  }
}

// LOSS曲线容器样式
.loss-curves-container {
  width: 100%;

  .loss-curves-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
  }
}

// Bland-Altman图表样式
.bland-altman-card {
  width: 100%;

  .bland-altman-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
  }
}
