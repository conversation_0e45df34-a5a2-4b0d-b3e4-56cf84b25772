# Cube Pipeline

Cube Pipeline 是一个基于 React 的机器学习平台前端项目，提供了流程编排、任务管理等功能。

## 技术栈

- React 17
- TypeScript
- Ant Design 4.x
- Redux Toolkit
- React Router 5
- i18next (国际化)
- Monaco Editor (代码编辑器)
- G6 (图可视化)
- React Flow (流程图)

## 开发环境要求

- Node.js >= 14
- npm 或 yarn

## 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

## 开发

```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev
```

## 构建

```bash
# 构建生产环境代码
npm run build

# 或使用 yarn
yarn build
```

## 代码格式化

```bash
# 格式化代码
npm run prettier

# 或使用 yarn
yarn prettier
```

## 项目结构

```
cube-pipeline/
├── src/           # 源代码目录
│   ├── api/       # API 接口
│   ├── components/ # 组件目录
│   ├── models/    # 数据模型
│   ├── pages/     # 页面组件
│   ├── routes/    # 路由配置
│   ├── utils/     # 工具函数
│   │   ├── config.ts  # 统一配置管理
│   │   └── index.ts   # 工具函数导出
│   └── types/     # 类型定义
├── public/        # 静态资源目录
├── config-overrides.js  # webpack 配置覆盖
└── package.json   # 项目依赖配置
```

## 配置管理

项目使用统一的配置管理，主要配置文件位于 `src/utils/config.ts`：

### JWT Token 管理
- 默认token存储在 `JWT_CONFIG.DEFAULT_TOKEN`
- localStorage中的token key为 `JWT_CONFIG.TOKEN_KEY`
- 使用 `getAuthorizationHeader()` 函数获取Authorization header
- 使用 `setToken(token)` 函数设置token
- 使用 `clearToken()` 函数清除token

### API 配置
- 请求超时时间：`API_CONFIG.TIMEOUT`
- 响应类型：`API_CONFIG.RESPONSE_TYPE`
- 默认请求头：`API_CONFIG.DEFAULT_HEADERS`

## 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 许可证

UNLICENSED 