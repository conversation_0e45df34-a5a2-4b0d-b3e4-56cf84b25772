import React, { useState, useEffect } from 'react';
import { Button, Tabs, Tooltip, Image, Modal, Form, Input, Select, message as Message } from 'antd';
import { ArrowLeftOutlined, EditOutlined, SaveOutlined, PlayCircleOutlined, SearchOutlined } from '@ant-design/icons';
import './style.less';
import image_1750324212163_t55rap from '../../public/assets/images/image_1750324212163_t55rap.svg';
import image_1750324218670_fbqa0b from '../../public/assets/images/image_1750324218670_fbqa0b.svg';
import image_1750324219274_r6x26t from '../../public/assets/images/image_1750324219274_r6x26t.svg';
import { useAppSelector, useAppDispatch } from '@src/models/hooks';
import { updateErrMsg, selectUserName } from '@src/models/app';
import { selectPipelineId, selectInfo, selectPipelineParams, updatePipelineParams, savePipeline } from '@src/models/pipeline';
import api from '@src/api';
import { saveTaskList, selectRunProgress } from '@src/models/task';

const { TabPane } = Tabs;
const { TextArea } = Input;

// 动态设置 tabItems
const getTabItems = (reportDisabled: boolean) => [
  { key: 'debug', label: '调试', disabled: false },
  { key: 'report', label: '报告', disabled: reportDisabled },
  { key: 'monitor', label: '监控', disabled: false },
];

// 定义props类型，增加onTabChange回调和activeKey属性
interface TopNavProps {
  onTabChange?: (activeKey: string) => void;
  activeKey?: string;
}

const TopNav: React.FC<TopNavProps> = ({ onTabChange, activeKey = 'debug' }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const dispatch = useAppDispatch();
  const pipelineId = useAppSelector(selectPipelineId);
  const info = useAppSelector(selectInfo);
  const userName = useAppSelector(selectUserName);
  const pipelineParamsStr = useAppSelector(selectPipelineParams);
  const runProgress = useAppSelector(selectRunProgress);
  // 只有 status === 'Succeeded' 时报告可用
  const reportTabDisabled = runProgress?.layout?.status !== 'Succeeded';
  const pipelineParamsObj = (() => {
    if (!pipelineParamsStr) {
      return { id: '', description: '', tags: [], operation: '' };
    }

    try {
      return JSON.parse(decodeURIComponent(pipelineParamsStr));
    } catch (error) {
      console.warn('解析pipelineParamsStr失败:', error);
      return { id: '', description: '', tags: [], operation: '' };
    }
  })();
  const { id, description, tags, operation } = pipelineParamsObj;
  const [allTags, setAllTags] = useState<any[]>([]);

  useEffect(() => {
    api.tag_modelview_list().then((res) => {
      if (Array.isArray(res?.result.data)) {
        setAllTags(res.result.data);
      }
    });
  }, []);

  // 打开弹窗
  const showModal = () => {
    setIsModalVisible(true);
  };

  // 确定时直接更新 Redux 状态
  const handleModalOk = async () => {
    try {
      let res;
      // 先构建基础数据
      const requestData: any = {
        description,
        tags: tags.join(','),
      };
      // 新增时需要 pipeline_id
      if (operation === 'add') {
        (requestData as any).pipeline_id = pipelineId;
      }

      if (operation === 'add') {
        res = await api.project_management_add(requestData);
      } else if (operation === 'edit') {
        res = await api.project_management_update(id, requestData);
      } else {
        Message.error('无效的操作类型');
        return;
      }

      if (res?.status === 0) {
        Message.success('保存成功');
        setIsModalVisible(false);
        // 如果是add，保存成功后切换为edit
        if (operation === 'add') {
          const newPipelineParams = {
            ...pipelineParamsObj,
            operation: 'edit',
            id: res.result.id,
          };
          dispatch(updatePipelineParams(JSON.stringify(newPipelineParams)));
        }
      } else {
        Message.error(res?.message || '保存失败');
      }
    } catch (e: any) {
      Message.error(e?.message || '保存异常');
    }
  };

  // 更新描述
  const handleDescriptionChange = (value: string) => {
    const newPipelineParams = {
      ...pipelineParamsObj,
      description: value,
    };
    dispatch(updatePipelineParams(JSON.stringify(newPipelineParams)));
  };

  // 更新标签
  const handleTagsChange = (value: string[]) => {
    const newPipelineParams = {
      ...pipelineParamsObj,
      tags: value,
    };
    dispatch(updatePipelineParams(JSON.stringify(newPipelineParams)));
  };

  return (
    <>
      <div className="topNavContainer">
        {/* 左侧返回和标题 */}
        <div className="topNavLeft">
          <Image
            src={image_1750324212163_t55rap}
            preview={false}
            alt="返回"
            style={{ width: 28, height: 28 }}
            className="topNavBack"
            onClick={() => {
              Modal.confirm({
                title: '确定要返回吗？',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                  // 根据环境区分端口号
                  const protocol = window.location.protocol; // 自动获取 http: 或 https:
                  const hostname = window.location.hostname;
                  const port = process.env.NODE_ENV === 'development' ? 3333 : window.location.port;
                  const targetUrl =
                    operation === 'add'
                      ? `${protocol}//${hostname}:${port}/frontend/smart-space/ai-model`
                      : `${protocol}//${hostname}:${port}/frontend/personal-space/project-management`;
                  window.open(targetUrl, '_blank');
                },
              });
            }}
          />
          <span className="topNavTitle" title={info?.name}>
            {info?.name}
          </span>
          <Image src={image_1750324218670_fbqa0b} preview={false} alt="编辑" className="topNavEdit" onClick={showModal} />
        </div>
        {/* 中间标签页 */}
        <div className="topNavTabs">
          <Tabs activeKey={activeKey} centered tabBarStyle={{ margin: 0 }} onChange={onTabChange}>
            {getTabItems(reportTabDisabled).map((tab) => (
              <TabPane tab={tab.label} key={tab.key} disabled={tab.disabled} />
            ))}
          </Tabs>
        </div>
        {/* 右侧按钮 */}
        <div className="topNavActions">
          <Button
            type="default"
            className="topNavSave"
            onClick={async (e) => {
              e?.preventDefault();
              dispatch(await saveTaskList());
              dispatch(savePipeline());
              Message.success('保存成功');
            }}
          >
            保存
          </Button>
          <Button
            type="primary"
            className="topNavRun"
            onClick={async () => {
              Modal.confirm({
                title: '确定要开始运行吗？',
                okText: '确定',
                cancelText: '取消',
                onOk: async () => {
                  // 运行流水线
                  await dispatch(await saveTaskList());
                  dispatch(savePipeline());
                  api.pipeline_modelview_run(pipelineId).then((res) => {
                    const { status, message } = res;
                    if (status === 0) {
                      Message.success(message);
                    } else {
                      Message.error(message);
                    }
                  });
                },
              });
            }}
          >
            运行
          </Button>
        </div>
      </div>
      <Modal
        title="保存设置"
        visible={isModalVisible}
        footer={[
          <Button key="back" onClick={() => setIsModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleModalOk}>
            确定
          </Button>,
        ]}
        className="common-modal"
        width={600}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} layout="horizontal">
          <Form.Item label="描述">
            <TextArea rows={4} value={description} onChange={(e) => handleDescriptionChange(e.target.value)} />
          </Form.Item>
          <Form.Item label="标签">
            <Select
              mode="multiple"
              value={tags.map(String)}
              style={{ width: '100%' }}
              options={allTags.map((tag: any) => ({ value: String(tag.id), label: tag.name }))}
              onChange={(value) => handleTagsChange(value)}
              placeholder="请选择标签"
            />
          </Form.Item>
          <Form.Item label="预览图">
            <Input placeholder="(非必填) 请输入图片地址、svg源码、或帮助文档链接" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TopNav;
