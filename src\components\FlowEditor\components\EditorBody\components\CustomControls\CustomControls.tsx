import React from 'react';
import './CustomControls.less';
import { Button, Image } from 'antd';
import image_1751438108161_x2ffv5 from '../../../../../../public/assets/images/image_1751438108161_x2ffv5.svg';
import image_1751438108806_llg7w7 from '../../../../../../public/assets/images/image_1751438108806_llg7w7.svg';
import image_1751438109369_acfnuf from '../../../../../../public/assets/images/image_1751438109369_acfnuf.svg';
import image_1751438110327_ctcyw4 from '../../../../../../public/assets/images/image_1751438110327_ctcyw4.svg';

interface CustomControlsProps {
  zoom: number; // 当前缩放比例（如0.7表示70%）
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitView: () => void;
  onTaggle: () => void;
  showMiniMap: boolean;
}

const CustomControls: React.FC<CustomControlsProps> = ({ zoom, onZoomIn, onZoomOut, onFitView, onTaggle, showMiniMap }) => {
  return (
    <div className="custom-controls">
      <div className="custom-controls-button">
        <Image src={image_1751438109369_acfnuf} preview={false} title="缩小" onClick={onZoomOut} />
      </div>
      <span className="percent">{Math.round(zoom * 100)}%</span>
      <div className="custom-controls-button">
        <Image src={image_1751438108161_x2ffv5} preview={false} title="放大" onClick={onZoomIn} />
      </div>
      <span className="divider" />
      <div className="custom-controls-button">
        <Image src={image_1751438110327_ctcyw4} preview={false} title="最大化" onClick={onFitView} />
      </div>
      <div className="custom-controls-button">
        <Image src={image_1751438108806_llg7w7} preview={false} title={showMiniMap ? '隐藏MiniMap' : '显示MiniMap'} onClick={onTaggle} />
      </div>
    </div>
  );
};

export default CustomControls;
