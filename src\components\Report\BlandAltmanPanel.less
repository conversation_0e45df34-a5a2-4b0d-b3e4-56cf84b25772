.bland-altman-panel {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 15px;
  position: relative;
  transition: all 0.2s ease;

  // 确保SVG在容器中居中
  display: flex;
  justify-content: center;
  align-items: center;

  // 悬浮效果
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  svg {
    display: block;
  }

  // 图表标题样式调整
  svg text[font-weight='500'] {
    font-size: 13px !important;
    fill: #333 !important;
    font-weight: 500 !important;
  }
}

// 图表内部样式优化
.bland-altman-panel {
  svg {
    .grid {
      stroke: #f0f0f0;
      stroke-width: 0.5px;
      opacity: 0.8;
    }

    text {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .dot {
      cursor: pointer;
      transition: all 0.2s ease;
      fill: #006bff !important;
      opacity: 0.7;

      &:hover {
        r: 4;
        opacity: 1;
      }
    }

    // 坐标轴样式
    .domain {
      stroke: #d9d9d9;
      stroke-width: 1px;
    }

    .tick {
      line {
        stroke: #d9d9d9;
        stroke-width: 1px;
      }

      text {
        fill: #666;
        font-size: 11px;
      }
    }

    // 轴标签样式
    text[text-anchor='middle'] {
      font-size: 11px !important;
      fill: #666 !important;
    }

    // 统计参考线样式
    line {
      &[stroke='#FF8D2F'] {
        stroke: #ff8d2f !important;
      }
    }
  }
}
