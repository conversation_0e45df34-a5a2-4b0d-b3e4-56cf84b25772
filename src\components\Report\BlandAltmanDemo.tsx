import React from 'react';
import BlandAltmanPanel from './BlandAltmanPanel';
import { generateBlandAltmanFromModelResults } from './utils/blandAltmanUtils';

// 使用提供的真实数据
const sampleData = {
  "val/val_loss": {
    "steps": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49],
    "values": [0.9545065760612488, 0.9534411430358887, 0.9475939869880676, 0.9432761073112488, 0.9442879557609558, 0.933349609375, 0.9481711387634277, 0.9423524737358093, 0.9368014335632324, 0.9033724665641785, 0.8891267776489258, 0.8856294751167297, 0.8938536643981934, 0.9012073874473572, 0.9032433032989502, 0.9241597056388855, 0.9319620132446289, 0.9311825633049011, 0.89315265417099, 0.8771800398826599, 0.879565417766571, 0.866438090801239, 0.8560929298400879, 0.8528453707695007, 0.8364925384521484]
  },
  "val/val_dice": {
    "steps": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49],
    "values": [0.08514931052923203, 0.1029437780380249, 0.07279983162879944, 0.04861807823181152, 0.04979438707232475, 0.06603553146123886, 0.05055787041783333, 0.06019871309399605, 0.07094180583953857, 0.17076754570007324, 0.26006218791007996, 0.29658791422843933, 0.18201106786727905, 0.1355813443660736, 0.12497176975011826, 0.08611568063497543, 0.07465445250272751, 0.0752968117594719, 0.1755925863981247, 0.2646564245223999, 0.24256829917430878, 0.31197577714920044, 0.39599141478538513, 0.44808050990104675, 0.5182819366455078]
  },
  "val/val_iou": {
    "steps": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49],
    "values": [0.045153260231018066, 0.054403215646743774, 0.03791201859712601, 0.024977587163448334, 0.025598658248782158, 0.03426153585314751, 0.026002757251262665, 0.03112819790840149, 0.03690186142921448, 0.09451282024383545, 0.1527109593153, 0.17776310443878174, 0.10099121183156967, 0.07318693399429321, 0.06704434007406235, 0.0451837033033371, 0.03890175372362137, 0.03923814743757248, 0.09735661000013351, 0.1546386331319809, 0.13952121138572693, 0.18765783309936523, 0.251688152551651, 0.2949253022670746, 0.3552514314651489]
  },
  "train/train_loss_epoch": {
    "steps": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49],
    "values": [0.9495410323143005, 0.9219645857810974, 0.9179812073707581, 0.8913764953613281, 0.8866881728172302, 0.876980721950531, 0.8875588178634644, 0.8918728232383728, 0.8863497972488403, 0.8850010633468628, 0.8846281170845032, 0.8743597269058228, 0.8709782361984253, 0.8870221376419067, 0.873115062713623, 0.8765281438827515, 0.8816412687301636, 0.8649482727050781, 0.866837739944458, 0.8627687692642212, 0.8657335638999939, 0.8564174771308899, 0.8531431555747986, 0.8574393391609192, 0.8414062261581421]
  },
  "test/test_loss": {
    "steps": [100],
    "values": [1]
  }
};

const BlandAltmanDemo: React.FC = () => {
  const blandAltmanData = generateBlandAltmanFromModelResults(sampleData);

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5' }}>
      <h2 style={{ marginBottom: '20px', color: '#333' }}>Bland-Altman 图表演示</h2>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '20px' }}>
        {/* 训练损失 vs 验证损失 */}
        {blandAltmanData.train && (
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <h3 style={{ marginBottom: '15px', color: '#333' }}>训练损失 vs 验证损失</h3>
            <BlandAltmanPanel 
              data={{ train: blandAltmanData.train }} 
              title="训练损失 vs 验证损失"
            />
            <p style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
              比较训练损失和验证损失在相同epoch的差异分布
            </p>
          </div>
        )}

        {/* DICE vs IOU 指标 */}
        {blandAltmanData.validation && (
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <h3 style={{ marginBottom: '15px', color: '#333' }}>DICE vs IOU 指标</h3>
            <BlandAltmanPanel 
              data={{ validation: blandAltmanData.validation }} 
              title="DICE vs IOU 指标"
            />
            <p style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
              比较验证集上DICE和IOU指标在相同epoch的差异分布
            </p>
          </div>
        )}

        {/* 测试损失 vs 验证损失 */}
        {blandAltmanData.test && (
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
            <h3 style={{ marginBottom: '15px', color: '#333' }}>测试损失 vs 验证损失</h3>
            <BlandAltmanPanel 
              data={{ test: blandAltmanData.test }} 
              title="测试损失 vs 验证损失"
            />
            <p style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
              比较测试损失和验证损失的差异分布
            </p>
          </div>
        )}
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <h3 style={{ marginBottom: '10px', color: '#333' }}>图表说明</h3>
        <ul style={{ color: '#666', fontSize: '14px', lineHeight: '1.6' }}>
          <li><strong>X轴 (Mean):</strong> 两个指标值的平均值</li>
          <li><strong>Y轴 (Difference):</strong> 两个指标值的差值</li>
          <li><strong>橙色实线:</strong> 差值的均值</li>
          <li><strong>橙色虚线:</strong> 95%一致性界限 (均值 ± 1.96×标准差)</li>
          <li><strong>蓝色散点:</strong> 各个时间点的数据</li>
        </ul>
      </div>
    </div>
  );
};

export default BlandAltmanDemo;
