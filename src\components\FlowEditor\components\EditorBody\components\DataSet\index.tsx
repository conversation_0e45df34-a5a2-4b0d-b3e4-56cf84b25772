import React, { useState, useEffect, FormEvent } from 'react';
import {
  Image,
  message,
  Modal,
  Radio,
  Space,
  Switch,
  Tooltip,
  Slider,
  InputNumber,
  Select,
  Input,
  Typography,
  Spin,
  Dropdown,
  Menu,
} from 'antd';
import { Handle, Position, NodeProps } from 'react-flow-renderer';
import Model from '../Model';
import './style.less';
import storage from '@src/utils/storage';
import { useAppDispatch, useAppSelector } from '@src/models/hooks';
import { savePipeline, selectPipelineId, updateEditing } from '@src/models/pipeline';
import { updateLoading } from '@src/models/task';
import api from '@src/api';
import { selectElements, updateElements } from '@src/models/element';
import { updateErrMsg } from '@src/models/app';
import { useTranslation } from 'react-i18next';
import { marked } from 'marked';
import { updateTaskList } from '@src/models/task';
import image_1751277358373_ma2p09 from '../../../../../../public/assets/images/image_1751277358373_ma2p09.svg';
import image_1751277368504_eyjy1y from '../../../../../../public/assets/images/image_1751277368504_eyjy1y.svg';
import image_1751278785433_fjuhee from '../../../../../../public/assets/images/image_1751278785433_fjuhee.svg';

const { TextArea } = Input;
const { Text } = Typography;
const { Option } = Select;

const DataSet: React.FC<NodeProps> = (props) => {
  const [visible, setVisible] = useState(false);
  const [recommendList, setRecommendList] = useState<any[]>([]);
  const [currentRecommend, setCurrentRecommend] = useState<any>();
  const [task, setTask] = useState<any>({});
  const [taskChanged, setTaskChanged] = useState<any>({});
  const [jobTemplate, setJobTemplate] = useState<any>({});
  const [templateArgs, setTemplateArgs] = useState<any>({});
  const [taskArgs, setTaskArgs] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [expand, setExpand] = useState(false);

  const jobTemplateStorage = storage.get('job_template');
  const dataMap = (jobTemplateStorage.value || []).reduce((pre: any, next: any) => ({ ...pre, [next.name]: next }), {});
  const pipelineId = useAppSelector(selectPipelineId);
  const elements = useAppSelector(selectElements);
  const dispatch = useAppDispatch();
  const { t, i18n } = useTranslation();

  // 配置变化事件
  const handleOnChange = (key: string, value: string | number | boolean | object, type?: string) => {
    const obj: any = {};
    let res = null;

    switch (type) {
      case 'json':
        try {
          res = JSON.parse(`${value}`);
        } catch (error) {
          res = value;
        }
        break;
      case 'int':
        res = +value;
        break;
      case 'float':
        res = +value;
        break;
      default:
        res = value;
        break;
    }

    if (type) {
      const args = JSON.parse(JSON.stringify(taskArgs));
      args[key] = res;
      obj.args = JSON.stringify(args);
    } else {
      obj[key] = value;
    }

    setTaskChanged({ ...taskChanged, ...obj });
    setTask({ ...task, ...obj });
    if (obj?.args) {
      setTaskArgs({ ...taskArgs, ...JSON.parse(obj.args) });
    }
  };

  // 运行任务
  const handleRunClick = () => {
    if (props.id) {
      api.task_modelview_run(+props.id).then((res) => {
        console.log(res);
      });
      message.success('任务开始运行');
    }
  };

  // 日志 - 跳转到监控页面
  const handleLogClick = () => {
    // 发送自定义事件来通知父组件切换到监控页面
    const event = new CustomEvent('switchToMonitor');
    window.dispatchEvent(event);
  };

  // 删除节点
  const handleDeleteClick = () => {
    if (!props.id) return;
    const taskId = +props.id;
    api
      .task_modelview_del(taskId)
      .then(() => {
        setTimeout(() => {
          dispatch(savePipeline());
        }, 2000);
        message.success(t('删除成功'));
      })
      .catch((err) => {
        if (err.response) {
          dispatch(updateErrMsg({ msg: err.response.data.message }));
        }
      })
      .finally(() => {
        dispatch(updateEditing(true));
        // 移除当前节点
        const newElements = elements.filter((ele) => ele.id !== props.id);
        dispatch(updateElements(newElements));
      });
  };

  // 从接口获取数据展示 task 配置面板
  useEffect(() => {
    if (props.id) {
      if (Object.keys(task).length === 0) {
        setLoading(true);
        api
          .task_modelview_get(+props.id)
          .then((res: any) => {
            if (res.status === 0) {
              const taskArgs = JSON.parse(res.result.args);
              const jobTemplate = res.result.job_template;
              const args = jobTemplate?.args ? JSON.parse(jobTemplate.args) : {};
              const initArgs = Object.keys(args).reduce((acc: any, cur: string) => {
                const current = args[cur];
                Object.keys(current).forEach((key: string) => {
                  acc[key] = current[key].default; // 参数的默认值
                });
                return acc;
              }, {});
              setTask(res.result);
              setTaskArgs(Object.assign(initArgs, taskArgs));
              setTemplateArgs(args);
              setJobTemplate(jobTemplate);
            }
          })
          .catch((err) => {
            if (err.response) {
              dispatch(updateErrMsg({ msg: err.response.data.message }));
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [props.id]);

  useEffect(() => {
    if (props.id) {
      dispatch(
        updateTaskList({
          id: +props.id,
          changed: taskChanged,
        }),
      );
    }
    if (taskChanged?.label) {
      const res = elements.map((ele) => {
        if (ele.id === props.id) {
          const data = { ...ele.data, ...{ label: taskChanged.label } };
          return { ...ele, ...{ data } };
        }
        return ele;
      });
      dispatch(updateElements(res));
    }
  }, [taskChanged]);

  return (
    <>
      <Modal
        title={t('智能推荐下游节点')}
        visible={visible}
        onCancel={() => {
          setCurrentRecommend(undefined);
          setVisible(false);
        }}
        onOk={() => {
          if (!currentRecommend) {
            message.warn(t('请先选择推荐节点'));
            return;
          }

          const modelInfo = dataMap[currentRecommend];
          const args = JSON.parse(modelInfo.args);
          const defaultArgs = {};

          Object.keys(args).reduce((acc, cur) => {
            const curArgs = {};

            Object.keys(args[cur]).reduce((curAcc, argsKey) => {
              const defaultValue = args[cur][argsKey].default;
              Object.assign(curAcc, { [argsKey]: defaultValue });
              return curAcc;
            }, curArgs);
            Object.assign(acc, curArgs);

            return acc;
          }, defaultArgs);

          const position = {
            x: props.xPos || 0,
            y: +(props?.yPos || 0) + 100,
          };

          if (pipelineId) {
            dispatch(updateLoading(true));
            const taskName = `${modelInfo.name.replace(/\.|[\u4e00-\u9fa5]/g, '').replace(/_|\s/g, '-') || 'task'}-${Date.now()}`.substring(
              0,
              49,
            );
            api
              .task_modelview_add(pipelineId, {
                job_template: modelInfo.id,
                pipeline: +pipelineId,
                name: taskName,
                label: `${t('新建')} ${modelInfo.name} ${t('任务')}`,
                volume_mount: 'kubeflow-user-workspace(pvc):/mnt,kubeflow-archives(pvc):/archives',
                image_pull_policy: 'Always',
                working_dir: '',
                command: '',
                overwrite_entrypoint: 0,
                node_selector: 'cpu=true,train=true',
                resource_memory: '2G',
                resource_cpu: '2',
                resource_gpu: '0',
                resource_rdma: '0',
                timeout: 0,
                retry: 0,
                args: JSON.stringify(defaultArgs),
              })
              .then((res: any) => {
                if (res?.result?.id) {
                  const newNode = {
                    id: `${res.result.id}`,
                    type: 'dataSet',
                    position,
                    data: {
                      info: modelInfo,
                      name: taskName,
                      label: `${t('新建')} ${modelInfo.name} ${t('任务')}`,
                    },
                  };
                  dispatch(updateEditing(true));
                  dispatch(updateElements(elements.concat(newNode)));
                  setTimeout(() => {
                    dispatch(savePipeline());
                  }, 2000);
                }
              })
              .catch((err) => {
                if (err.response) {
                  dispatch(updateErrMsg({ msg: err.response.data.message }));
                }
              })
              .finally(() => {
                dispatch(updateLoading(false));
                setVisible(false);
              });
          }
        }}
      >
        <div>
          <Radio.Group
            onChange={(e) => {
              setCurrentRecommend(e.target.value);
            }}
            value={currentRecommend}
          >
            <Space direction="vertical">
              {recommendList.map((item: any) => {
                return (
                  <Radio value={item.name} key={`recommend_${item.name}`}>
                    {item.name}
                  </Radio>
                );
              })}
            </Space>
          </Radio.Group>
        </div>
      </Modal>

      <Handle type="target" position={Position.Left} className="handleStyle" />
      <div className={props.selected ? 'nodeOnSelect' : 'nodeContainer'}>
        <div className="nodeContentWrapper">
          <div className="nodeTitleBar">
            <div className="nodeTitle">
              <Image src={props?.data?.info?.icon || props?.data?.icon} preview={false} />
              <span title={props?.data?.label}>{props?.data?.label}</span>
            </div>
            <div className="iconGroup">
              {/* 展开/收起 */}
              {Object.keys(templateArgs).length > 0 && (
                <>
                  {expand ? (
                    <Image src={image_1751277368504_eyjy1y} preview={false} onClick={() => setExpand(false)} />
                  ) : (
                    <Image
                      src={image_1751277368504_eyjy1y}
                      style={{ transform: 'rotate(180deg)' }}
                      preview={false}
                      onClick={() => setExpand(true)}
                    />
                  )}
                </>
              )}
              {/* 运行/停止 */}
              <Image src={image_1751278785433_fjuhee} style={{ marginLeft: '2px' }} preview={false} onClick={() => handleRunClick()} />
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item key="log" className="menuItem" onClick={handleLogClick}>
                      日志
                    </Menu.Item>
                    <Menu.Item key="delete" className="menuItem" onClick={handleDeleteClick}>
                      删除
                    </Menu.Item>
                  </Menu>
                }
                trigger={['hover']}
                placement="bottomLeft"
              >
                <Image src={image_1751277358373_ma2p09} preview={false} style={{ cursor: 'pointer' }} />
              </Dropdown>
            </div>
          </div>

          {/* 动态参数平铺显示 */}
          {expand && !loading && Object.keys(templateArgs).length > 0 && (
            <div className="parametersContainer">
              {Object.keys(templateArgs).map((cur) => {
                const current = templateArgs[cur];
                return (
                  <div key={cur} className="parameterGroup">
                    <div className="parameterGroupTitle">{cur}</div>
                    {Object.keys(current).map((key) => {
                      const args = current[key];
                      const tip = marked(args.tip || '', { async: false }) as string;
                      const tip_tooltip = (
                        <Tooltip title={tip}>
                          <span className="tipLink">{t('详情')}</span>
                        </Tooltip>
                      );

                      const { choice } = args;
                      type Option = {
                        key: string;
                        text: string;
                      };
                      let options: Option[] = [];
                      if (Array.isArray(choice)) {
                        options = choice.map((option) => ({ key: option, text: option }));
                      } else if (typeof choice === 'object' && choice !== null) {
                        options = Object.keys(choice).map((key) => ({ key: key, text: choice[key] }));
                      }

                      const keyArgs = taskArgs && taskArgs[key];
                      const keyValue = args.type === 'json' ? JSON.stringify(keyArgs, undefined, 4) : keyArgs;

                      // 根据参数类型渲染不同的控件
                      if (args.type === 'float') {
                        const range = typeof args.range === 'string' ? args.range.split(',') : args.range;
                        return (
                          <div key={key} className="parameterItem">
                            <Tooltip title={args.describe}>
                              <div className="parameterItemLabel" title={key}>
                                {key}
                              </div>
                            </Tooltip>
                            <Slider
                              min={range?.[0] || 0}
                              max={range?.[1] || 1}
                              step={args.step || 0.1}
                              onChange={(value: number) => {
                                handleOnChange(key, value, args.type);
                              }}
                              value={keyValue}
                              disabled={args.editable !== 1}
                            />
                          </div>
                        );
                      }

                      if (args.type === 'int') {
                        const range = typeof args.range === 'string' ? args.range.split(',') : args.range;
                        return (
                          <div key={key} className="parameterItem">
                            <Tooltip title={args.describe}>
                              <div className="parameterItemLabel" title={key}>
                                {key}
                              </div>
                            </Tooltip>
                            <InputNumber
                              min={range || 0}
                              max={range || 100}
                              step={args.step || 1}
                              onChange={(value: number | null) => {
                                handleOnChange(key, value || 0, args.type);
                              }}
                              value={keyValue}
                              disabled={args.editable !== 1}
                              style={{ width: '100%' }}
                            />
                          </div>
                        );
                      }

                      if (args.type === 'bool') {
                        return (
                          <div key={key} className="parameterItem">
                            <Tooltip title={args.describe}>
                              <div className="parameterItemLabel" title={key}>
                                {key}
                              </div>
                            </Tooltip>
                            <Switch
                              checked={keyValue}
                              onChange={(checked: boolean) => {
                                handleOnChange(key, checked, args.type);
                              }}
                              disabled={args.editable !== 1}
                            />
                          </div>
                        );
                      }

                      if (args.type === 'list') {
                        const selectedKeys = (typeof keyValue === 'string' ? keyValue : args.default).split(',');
                        return (
                          <div key={key} className="parameterItem">
                            <Tooltip title={args.describe}>
                              <div className="parameterItemLabel" title={key}>
                                {key}
                              </div>
                            </Tooltip>
                            <Select
                              onChange={(values: string[]) => {
                                const newSelectedKeys_str = values?.join(',');
                                handleOnChange(key, newSelectedKeys_str || '', args.type);
                              }}
                              value={selectedKeys}
                              mode="multiple"
                              disabled={args.editable !== 1}
                              style={{ width: '100%' }}
                              placeholder={args.require === 1 ? t('请选择') : ''}
                            >
                              {options.map((option) => (
                                <Option key={option.key} value={option.key}>
                                  {option.text}
                                </Option>
                              ))}
                            </Select>
                          </div>
                        );
                      }

                      return (
                        <div key={key} className="parameterItem">
                          {options.length > 0 ? (
                            <>
                              <Tooltip title={args.describe}>
                                <div className="parameterItemLabel" title={key}>
                                  {key}
                                </div>
                              </Tooltip>
                              <Select
                                onChange={(value: string) => {
                                  handleOnChange(key, value, args.type);
                                }}
                                value={keyValue || args.default}
                                disabled={args.editable !== 1}
                                placeholder={args.require === 1 ? t('请选择') : ''}
                              >
                                {options.map((option) => (
                                  <Option key={option.key} value={option.key}>
                                    {option.text}
                                  </Option>
                                ))}
                              </Select>
                            </>
                          ) : (
                            <>
                              <Tooltip title={args.describe}>
                                <div className="parameterItemLabel" title={key}>
                                  {key}
                                </div>
                              </Tooltip>
                              <Input
                                title={args.describe}
                                onChange={(e) => {
                                  handleOnChange(key, e.target.value, args.type);
                                }}
                                value={keyValue}
                                disabled={args.editable !== 1 || args.type === 'json'}
                                className="inputField"
                                placeholder={args.require === 1 ? t('请输入') : ''}
                                onMouseDown={(e) => e.stopPropagation()} // 新增，阻止冒泡
                              />
                            </>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          )}

          {/* 加载状态 */}
          {loading && (
            <div className="loadingContainer">
              <Spin size="small" />
              <div className="loadingText">{t('正在加载参数配置...')}</div>
            </div>
          )}
        </div>
      </div>
      <Handle
        type="source"
        onClick={() => {
          console.log('props', props);
          let recommendObj = props.data?.info?.expand;
          if (Object.prototype.toString.call(recommendObj) === '[object String]') {
            recommendObj = JSON.parse(props.data?.info?.expand || '{}');
          }
          const recommend = recommendObj.rec_job_template;
          if (recommend) {
            setRecommendList([dataMap[recommend]]);
            setVisible(true);
          }
        }}
        position={Position.Right}
        className="handleStyle"
      />
    </>
  );
};

export default DataSet;
